grammar FlinkSql;

// 词法规则
// 关键字
CREATE: C R E A T E;
TABLE: T A B L E;
VIEW: V I E W;
SELECT: S E L E C T;
FROM: F R O M;
WHERE: W H E R E;
INSERT: I N S E R T;
INTO: I N T O;
VALUES: V A L U E S;
WITH: W I T H;
AS: A S;
AND: A N D;
OR: O R;
NOT: N O T;
NULL: N U L L;
TRUE: T R U E;
FALSE: F A L S E;
IF: I F;
EXISTS: E X I S T S;
WATERMARK: W A T E R M A R K;
FOR: F O R;
PROCTIME: P R O C T I M E;

// 数据类型
VARCHAR: V A R C H A R;
INT: I N T;
BIGINT: B I G I N T;
DECIMAL: D E C I M A L;
TIMESTAMP: T I M E S T A M P;
DATE: D A T E;
TIME: T I M E;
BOOLEAN: B O O L E A N;

// 操作符
EQUALS: '=';
NOT_EQUALS: '<>' | '!=';
LESS_THAN: '<';
GREATER_THAN: '>';
LESS_EQUAL: '<=';
GREATER_EQUAL: '>=';
PLUS: '+';
MINUS: '-';
MULTIPLY: '*';
DIVIDE: '/';
MODULO: '%';

// 分隔符
SEMICOLON: ';';
COMMA: ',';
DOT: '.';
LEFT_PAREN: '(';
RIGHT_PAREN: ')';
LEFT_BRACKET: '[';
RIGHT_BRACKET: ']';
LEFT_BRACE: '{';
RIGHT_BRACE: '}';

// 标识符和字面量
IDENTIFIER: [a-zA-Z_][a-zA-Z0-9_]*;
QUOTED_IDENTIFIER: '`' (~'`')* '`';
STRING_LITERAL: '\'' (~'\'')* '\'';
NUMBER: [0-9]+ ('.' [0-9]+)?;

// 空白字符和注释
WS: [ \t\r\n]+ -> skip;
LINE_COMMENT: '--' ~[\r\n]* -> skip;
BLOCK_COMMENT: '/*' .*? '*/' -> skip;

// 语法规则
sqlStatement
    : createTableStatement
    | selectStatement
    | insertStatement
    | EOF
    ;

createTableStatement
    : CREATE TABLE tableName LEFT_PAREN columnDefinitionList RIGHT_PAREN withClause?
    ;

tableName
    : IDENTIFIER
    | QUOTED_IDENTIFIER
    ;

columnDefinitionList
    : columnDefinition (COMMA columnDefinition)*
    ;

columnDefinition
    : columnName dataType columnConstraint*
    | watermarkDefinition
    ;

columnName
    : IDENTIFIER
    | QUOTED_IDENTIFIER
    ;

dataType
    : VARCHAR (LEFT_PAREN NUMBER RIGHT_PAREN)?
    | INT
    | BIGINT
    | DECIMAL (LEFT_PAREN NUMBER (COMMA NUMBER)? RIGHT_PAREN)?
    | TIMESTAMP (LEFT_PAREN NUMBER RIGHT_PAREN)?
    | DATE
    | TIME
    | BOOLEAN
    ;

columnConstraint
    : NOT NULL
    | NULL
    ;

watermarkDefinition
    : WATERMARK FOR columnName AS expression
    ;

withClause
    : WITH LEFT_PAREN propertyList RIGHT_PAREN
    ;

propertyList
    : property (COMMA property)*
    ;

property
    : STRING_LITERAL EQUALS STRING_LITERAL
    ;

selectStatement
    : SELECT selectList FROM tableReference whereClause?
    ;

selectList
    : selectItem (COMMA selectItem)*
    | MULTIPLY
    ;

selectItem
    : expression (AS? columnAlias)?
    ;

columnAlias
    : IDENTIFIER
    | QUOTED_IDENTIFIER
    ;

tableReference
    : tableName (AS? tableAlias)?
    ;

tableAlias
    : IDENTIFIER
    | QUOTED_IDENTIFIER
    ;

whereClause
    : WHERE expression
    ;

insertStatement
    : INSERT INTO tableName (LEFT_PAREN columnNameList RIGHT_PAREN)? selectStatement
    | INSERT INTO tableName (LEFT_PAREN columnNameList RIGHT_PAREN)? VALUES valuesList
    ;

columnNameList
    : columnName (COMMA columnName)*
    ;

valuesList
    : LEFT_PAREN expressionList RIGHT_PAREN (COMMA LEFT_PAREN expressionList RIGHT_PAREN)*
    ;

expressionList
    : expression (COMMA expression)*
    ;

expression
    : primaryExpression
    | expression binaryOperator expression
    | unaryOperator expression
    | functionCall
    | LEFT_PAREN expression RIGHT_PAREN
    ;

primaryExpression
    : columnReference
    | literal
    ;

columnReference
    : (tableName DOT)? columnName
    ;

literal
    : STRING_LITERAL
    | NUMBER
    | TRUE
    | FALSE
    | NULL
    ;

functionCall
    : functionName LEFT_PAREN (expressionList)? RIGHT_PAREN
    | PROCTIME LEFT_PAREN RIGHT_PAREN
    ;

functionName
    : IDENTIFIER
    ;

binaryOperator
    : EQUALS
    | NOT_EQUALS
    | LESS_THAN
    | GREATER_THAN
    | LESS_EQUAL
    | GREATER_EQUAL
    | PLUS
    | MINUS
    | MULTIPLY
    | DIVIDE
    | MODULO
    | AND
    | OR
    ;

unaryOperator
    : NOT
    | MINUS
    | PLUS
    ;

// 字符片段（用于关键字定义）
fragment A: [aA];
fragment B: [bB];
fragment C: [cC];
fragment D: [dD];
fragment E: [eE];
fragment F: [fF];
fragment G: [gG];
fragment H: [hH];
fragment I: [iI];
fragment J: [jJ];
fragment K: [kK];
fragment L: [lL];
fragment M: [mM];
fragment N: [nN];
fragment O: [oO];
fragment P: [pP];
fragment Q: [qQ];
fragment R: [rR];
fragment S: [sS];
fragment T: [tT];
fragment U: [uU];
fragment V: [vV];
fragment W: [wW];
fragment X: [xX];
fragment Y: [yY];
fragment Z: [zZ];
