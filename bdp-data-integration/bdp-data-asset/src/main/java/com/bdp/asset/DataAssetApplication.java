package com.bdp.asset;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/27 14:19
 */
@SpringBootApplication(scanBasePackages = "com.bdp")
@MapperScan("com.bdp.asset.dao")
@EntityScan(basePackages = "com.bdp")
@EnableFeignClients(basePackages = {"com.bdp.asset.rpc","com.bdp.auth.dep.rpc", "com.bdp.**.rpc", "com.bdp.**.feign"})

@Slf4j
public class DataAssetApplication {
	public static void main(String[] args) throws UnknownHostException {
		ConfigurableApplicationContext application = SpringApplication.run(DataAssetApplication.class, args);
		Environment env = application.getEnvironment();
		log.info("\n----------------------------------------------------------\n\t" +
						"应用 '{}' 运行成功! 访问连接:\n\t" +
						"Swagger文档: \t\thttp://{}:{}/swagger-ui.html\n\t" +
						"----------------------------------------------------------",
				env.getProperty("spring.application.name"),
				InetAddress.getLocalHost().getHostAddress(),
				env.getProperty("server.port"));
	}
}
