# 血缘解析功能

## 概述

本模块实现了完整的SQL血缘关系解析功能，支持字段级血缘关系提取，能够解析多种类型的SQL语句，包括Flink SQL、MySQL、PostgreSQL、Oracle等，并将解析结果存储到MySQL数据库中。

## 功能特性

### 1. 多SQL类型支持
- **Flink SQL**: 支持CREATE TABLE、SELECT、INSERT等语句，特别针对Flink的连接器语法进行优化
- **MySQL**: 支持MySQL特有的语法和函数
- **PostgreSQL**: 支持PostgreSQL特有的数据类型和语法
- **Oracle**: 支持Oracle特有的语法
- **SQL Server**: 支持SQL Server特有的语法
- **Hive**: 支持Hive SQL语法
- **通用SQL**: 支持标准SQL语法

### 2. 解析引擎
- **Apache Calcite**: 主要解析引擎，支持标准SQL解析
- **ANTLR4**: 自定义语法解析，支持复杂的SQL方言
- **正则表达式**: 简单SQL的快速解析

### 3. 血缘关系类型
- **字段级血缘**: 精确到字段的血缘关系
- **表级血缘**: 表与表之间的依赖关系
- **数据源级血缘**: 数据源之间的关系

### 4. 数据源格式
支持多种数据源格式，特别是Flink SQL中的连接器：
- **Kafka**: `kafka.*************:9092`
- **MySQL**: `mysql.192.168.1.100:3306`
- **PostgreSQL**: `postgresql.192.168.1.100:5432`
- **Elasticsearch**: `elasticsearch.192.168.1.150:9200`

## 架构设计

### 核心接口
```java
public interface SQLParser {
    LineageResult parse(String sql) throws SQLParseException;
    String getSupportedSqlType();
    boolean supports(String sql);
}
```

### 主要组件

#### 1. 解析器工厂 (SqlParseFactory)
负责根据SQL类型创建相应的解析器实例。

#### 2. 解析器实现
- `FlinkSqlParser`: Flink SQL解析器
- `MysqlSqlParser`: MySQL解析器
- `CommonSQLParser`: 通用SQL解析器
- 其他数据库特定解析器

#### 3. 数据模型
- `DataSource`: 数据源模型
- `Table`: 表模型
- `Field`: 字段模型
- `LineageRelation`: 血缘关系模型
- `LineageResult`: 解析结果模型

#### 4. 存储层
- `LineageEntity`: 血缘关系实体
- `LineageDao`: 数据访问层
- `LineageService`: 业务服务层

## 使用方法

### 1. 基本用法

```java
@Autowired
private LineageService lineageService;

// 自动检测SQL类型并解析
String sql = "SELECT id, name FROM users WHERE status = 'active'";
LineageResult result = lineageService.parseAndSave(sql);

// 指定SQL类型解析
LineageResult result = lineageService.parseAndSave(sql, "MYSQL");
```

### 2. Flink SQL示例

```java
String flinkSql = """
    CREATE TABLE kafka_source (
        id BIGINT,
        name VARCHAR(255),
        event_time TIMESTAMP(3),
        WATERMARK FOR event_time AS event_time - INTERVAL '5' SECOND
    ) WITH (
        'connector' = 'kafka',
        'topic' = 'user_events',
        'properties.bootstrap.servers' = '*************:9092',
        'format' = 'json'
    )
    """;

LineageResult result = lineageService.parseAndSave(flinkSql);
```

### 3. 查询血缘关系

```java
// 根据源表查询
List<LineageEntity> lineages = lineageService.findBySourceTable(
    "kafka.*************:9092", "user_events");

// 根据目标字段查询
List<LineageEntity> lineages = lineageService.findByTargetField(
    "mysql.localhost:3306.analytics.user_summary.total_events");

// 分页查询
IPage<LineageEntity> page = lineageService.findBySqlType("FLINK_SQL", 1, 10);
```

### 4. REST API

```bash
# 解析SQL
POST /api/lineage/parse
{
    "sql": "SELECT id, name FROM users",
    "sqlType": "MYSQL"
}

# 查询血缘关系
GET /api/lineage/source-table?sourceDatasourceKey=mysql.localhost:3306&sourceTableName=users

# 获取统计信息
GET /api/lineage/statistics
```

## 配置

### application.yml
```yaml
bdp:
  lineage:
    enabled: true
    defaultSqlType: COMMON_SQL
    parseTimeoutSeconds: 30
    autoSave: true
    maxSqlLength: 1000000
    cacheEnabled: true
    cacheSize: 1000
    cacheExpireMinutes: 60
    antlrEnabled: false
    calciteEnabled: true
    parser:
      strictMode: false
      ignoreCase: true
      parseComments: false
      maxParseDepth: 100
      enableTypeInference: true
```

## 数据库表结构

### 血缘关系表 (lineage_relation)
存储字段级血缘关系信息，包括源字段、目标字段、转换表达式等。

### 数据源信息表 (datasource_info)
存储数据源的基本信息和连接参数。

### 表信息表 (table_info)
存储表的元数据信息。

### 字段信息表 (field_info)
存储字段的详细信息。

## 扩展开发

### 1. 添加新的SQL类型支持

```java
public class CustomSqlParser extends AbstractSQLParser {
    @Override
    public String getSupportedSqlType() {
        return "CUSTOM_SQL";
    }
    
    @Override
    public boolean supports(String sql) {
        return sql.contains("CUSTOM_KEYWORD");
    }
    
    @Override
    protected void doParseInternal(String sql, LineageResult result) {
        // 实现自定义解析逻辑
    }
}

// 注册到工厂
sqlParseFactory.registerParser("CUSTOM_SQL", CustomSqlParser.class);
```

### 2. 自定义ANTLR语法

在 `src/main/antlr4` 目录下创建 `.g4` 语法文件，Maven会自动生成对应的解析器类。

### 3. 扩展数据模型

可以通过继承现有的数据模型类来添加特定的属性和方法。

## 测试

### 运行测试
```bash
mvn test
```

### 测试覆盖
- 单元测试：测试各个解析器的功能
- 集成测试：测试完整的解析和存储流程
- 性能测试：测试大SQL的解析性能

## 注意事项

1. **性能考虑**: 对于大型SQL语句，解析可能需要较长时间
2. **内存使用**: Calcite解析器可能消耗较多内存
3. **SQL兼容性**: 不同数据库的SQL方言可能需要特殊处理
4. **错误处理**: 复杂SQL可能解析失败，需要适当的错误处理

## 后续优化

1. **增强ANTLR解析器**: 完善自定义语法解析
2. **优化性能**: 添加解析缓存和并行处理
3. **扩展数据源**: 支持更多类型的数据源
4. **可视化界面**: 提供血缘关系的图形化展示
5. **实时解析**: 支持流式SQL的实时血缘解析
