package com.bdp.asset.test.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import lombok.Data;

/**
 * 血缘解析配置类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Configuration
@ComponentScan(basePackages = "com.bdp.asset.test")
@EnableConfigurationProperties(LineageConfig.LineageProperties.class)
public class LineageConfig {
    
    /**
     * 血缘解析配置属性
     */
    @Data
    @ConfigurationProperties(prefix = "bdp.lineage")
    public static class LineageProperties {
        
        /**
         * 是否启用血缘解析
         */
        private boolean enabled = true;
        
        /**
         * 默认SQL类型
         */
        private String defaultSqlType = "COMMON_SQL";
        
        /**
         * 解析超时时间（秒）
         */
        private int parseTimeoutSeconds = 30;
        
        /**
         * 是否自动保存解析结果
         */
        private boolean autoSave = true;
        
        /**
         * 最大SQL长度
         */
        private int maxSqlLength = 1000000;
        
        /**
         * 是否启用缓存
         */
        private boolean cacheEnabled = true;
        
        /**
         * 缓存大小
         */
        private int cacheSize = 1000;
        
        /**
         * 缓存过期时间（分钟）
         */
        private int cacheExpireMinutes = 60;
        
        /**
         * 是否启用ANTLR解析
         */
        private boolean antlrEnabled = false;
        
        /**
         * 是否启用Calcite解析
         */
        private boolean calciteEnabled = true;
        
        /**
         * 解析器配置
         */
        private ParserConfig parser = new ParserConfig();
        
        @Data
        public static class ParserConfig {
            
            /**
             * 是否严格模式
             */
            private boolean strictMode = false;
            
            /**
             * 是否忽略大小写
             */
            private boolean ignoreCase = true;
            
            /**
             * 是否解析注释
             */
            private boolean parseComments = false;
            
            /**
             * 最大解析深度
             */
            private int maxParseDepth = 100;
            
            /**
             * 是否启用字段类型推断
             */
            private boolean enableTypeInference = true;
        }
    }
}
