package com.bdp.asset.test.controller;

import com.bdp.asset.test.entity.LineageEntity;
import com.bdp.asset.test.exception.SQLParseException;
import com.bdp.asset.test.model.LineageResult;
import com.bdp.asset.test.service.LineageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 血缘关系控制器
 * 提供血缘关系解析和查询的REST API
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/lineage")
@Api(tags = "血缘关系管理")
public class LineageController {

    private static final Logger logger = LoggerFactory.getLogger(LineageController.class);

    @Autowired
    private LineageService lineageService;

    /**
     * 解析SQL并保存血缘关系
     */
    @PostMapping("/parse")
    @ApiOperation("解析SQL并保存血缘关系")
    public ResponseEntity<Map<String, Object>> parseAndSave(
            @ApiParam("SQL语句") @RequestBody Map<String, String> request) {

        Map<String, Object> response = new HashMap<>();

        try {
            String sql = request.get("sql");
            String sqlType = request.get("sqlType");

            if (sql == null || sql.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "SQL语句不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            LineageResult result;
            if (sqlType != null && !sqlType.trim().isEmpty()) {
                result = lineageService.parseAndSave(sql, sqlType);
            } else {
                result = lineageService.parseAndSave(sql);
            }

            response.put("success", result.isSuccess());
            response.put("message", result.isSuccess() ? "解析成功" : "解析失败");
            response.put("data", result);

            return ResponseEntity.ok(response);

        } catch (SQLParseException e) {
            logger.error("SQL解析失败", e);
            response.put("success", false);
            response.put("message", "SQL解析失败: " + e.getMessage());
            response.put("error", e.toString());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            logger.error("系统异常", e);
            response.put("success", false);
            response.put("message", "系统异常: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据源表查询血缘关系
     */
    @GetMapping("/source-table")
    @ApiOperation("根据源表查询血缘关系")
    public ResponseEntity<Map<String, Object>> findBySourceTable(
            @ApiParam("源数据源Key") @RequestParam String sourceDatasourceKey,
            @ApiParam("源表名") @RequestParam String sourceTableName) {

        Map<String, Object> response = new HashMap<>();

        try {
            List<LineageEntity> lineages = lineageService.findBySourceTable(sourceDatasourceKey, sourceTableName);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", lineages);
            response.put("total", lineages.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询血缘关系失败", e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据目标表查询血缘关系
     */
    @GetMapping("/target-table")
    @ApiOperation("根据目标表查询血缘关系")
    public ResponseEntity<Map<String, Object>> findByTargetTable(
            @ApiParam("目标数据源Key") @RequestParam String targetDatasourceKey,
            @ApiParam("目标表名") @RequestParam String targetTableName) {

        Map<String, Object> response = new HashMap<>();

        try {
            List<LineageEntity> lineages = lineageService.findByTargetTable(targetDatasourceKey, targetTableName);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", lineages);
            response.put("total", lineages.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询血缘关系失败", e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据字段查询血缘关系
     */
    @GetMapping("/field")
    @ApiOperation("根据字段查询血缘关系")
    public ResponseEntity<Map<String, Object>> findByField(
            @ApiParam("字段完整路径") @RequestParam String fieldPath,
            @ApiParam("查询类型：source或target") @RequestParam(defaultValue = "source") String type) {

        Map<String, Object> response = new HashMap<>();

        try {
            List<LineageEntity> lineages;
            if ("target".equalsIgnoreCase(type)) {
                lineages = lineageService.findByTargetField(fieldPath);
            } else {
                lineages = lineageService.findBySourceField(fieldPath);
            }

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", lineages);
            response.put("total", lineages.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询血缘关系失败", e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取血缘关系统计信息")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            Long totalCount = lineageService.countTotal();
            String[] supportedTypes = lineageService.getSupportedSqlTypes();

            Map<String, Long> typeStatistics = new HashMap<>();
            for (String type : supportedTypes) {
                Long count = lineageService.countBySqlType(type);
                typeStatistics.put(type, count);
            }

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", totalCount);
            statistics.put("supportedTypes", supportedTypes);
            statistics.put("typeStatistics", typeStatistics);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", statistics);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
    

