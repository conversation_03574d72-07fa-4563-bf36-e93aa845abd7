-- 血缘关系表结构
-- 用于存储字段级血缘关系信息

CREATE TABLE IF NOT EXISTS `lineage_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_datasource_type` varchar(50) DEFAULT NULL COMMENT '源数据源类型',
  `source_datasource_address` varchar(255) DEFAULT NULL COMMENT '源数据源地址',
  `source_datasource_key` varchar(255) DEFAULT NULL COMMENT '源数据源Key',
  `source_table_name` varchar(255) DEFAULT NULL COMMENT '源表名',
  `source_field_name` varchar(255) DEFAULT NULL COMMENT '源字段名',
  `source_field_path` varchar(500) DEFAULT NULL COMMENT '源字段完整路径',
  `target_datasource_type` varchar(50) DEFAULT NULL COMMENT '目标数据源类型',
  `target_datasource_address` varchar(255) DEFAULT NULL COMMENT '目标数据源地址',
  `target_datasource_key` varchar(255) DEFAULT NULL COMMENT '目标数据源Key',
  `target_table_name` varchar(255) DEFAULT NULL COMMENT '目标表名',
  `target_field_name` varchar(255) DEFAULT NULL COMMENT '目标字段名',
  `target_field_path` varchar(500) DEFAULT NULL COMMENT '目标字段完整路径',
  `transform_expression` text COMMENT '转换表达式',
  `transform_type` varchar(50) DEFAULT NULL COMMENT '转换类型',
  `function_name` varchar(100) DEFAULT NULL COMMENT '使用的函数名',
  `relation_type` varchar(50) DEFAULT NULL COMMENT '关系类型',
  `confidence` decimal(3,2) DEFAULT NULL COMMENT '置信度',
  `sql_type` varchar(50) DEFAULT NULL COMMENT 'SQL类型',
  `original_sql` text COMMENT '原始SQL语句',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 (0: 未删除, 1: 已删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_source_datasource_key` (`source_datasource_key`),
  KEY `idx_source_table_name` (`source_table_name`),
  KEY `idx_source_field_path` (`source_field_path`),
  KEY `idx_target_datasource_key` (`target_datasource_key`),
  KEY `idx_target_table_name` (`target_table_name`),
  KEY `idx_target_field_path` (`target_field_path`),
  KEY `idx_sql_type` (`sql_type`),
  KEY `idx_transform_type` (`transform_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='血缘关系表';

-- 数据源信息表
CREATE TABLE IF NOT EXISTS `datasource_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_key` varchar(255) NOT NULL COMMENT '数据源Key',
  `source_type` varchar(50) NOT NULL COMMENT '数据源类型',
  `source_address` varchar(255) NOT NULL COMMENT '数据源地址',
  `source_name` varchar(255) DEFAULT NULL COMMENT '数据源名称',
  `database_name` varchar(255) DEFAULT NULL COMMENT '数据库名称',
  `properties` json DEFAULT NULL COMMENT '连接属性',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 (0: 禁用, 1: 启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 (0: 未删除, 1: 已删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_source_key` (`source_key`),
  KEY `idx_source_type` (`source_type`),
  KEY `idx_source_address` (`source_address`),
  KEY `idx_status` (`status`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源信息表';

-- 表信息表
CREATE TABLE IF NOT EXISTS `table_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `datasource_key` varchar(255) NOT NULL COMMENT '数据源Key',
  `table_name` varchar(255) NOT NULL COMMENT '表名',
  `table_alias` varchar(255) DEFAULT NULL COMMENT '表别名',
  `schema_name` varchar(255) DEFAULT NULL COMMENT '模式名/数据库名',
  `table_type` varchar(50) DEFAULT NULL COMMENT '表类型',
  `table_comment` varchar(500) DEFAULT NULL COMMENT '表注释',
  `field_count` int(11) DEFAULT '0' COMMENT '字段数量',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 (0: 未删除, 1: 已删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_datasource_table` (`datasource_key`, `table_name`),
  KEY `idx_datasource_key` (`datasource_key`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_table_type` (`table_type`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表信息表';

-- 字段信息表
CREATE TABLE IF NOT EXISTS `field_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_id` bigint(20) NOT NULL COMMENT '表ID',
  `field_name` varchar(255) NOT NULL COMMENT '字段名',
  `field_alias` varchar(255) DEFAULT NULL COMMENT '字段别名',
  `data_type` varchar(100) DEFAULT NULL COMMENT '字段数据类型',
  `field_length` int(11) DEFAULT NULL COMMENT '字段长度',
  `field_precision` int(11) DEFAULT NULL COMMENT '精度',
  `field_scale` int(11) DEFAULT NULL COMMENT '小数位数',
  `is_nullable` tinyint(1) DEFAULT '1' COMMENT '是否可为空',
  `is_primary_key` tinyint(1) DEFAULT '0' COMMENT '是否为主键',
  `default_value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `field_comment` varchar(500) DEFAULT NULL COMMENT '字段注释',
  `field_order` int(11) DEFAULT '0' COMMENT '字段顺序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 (0: 未删除, 1: 已删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_field` (`table_id`, `field_name`),
  KEY `idx_table_id` (`table_id`),
  KEY `idx_field_name` (`field_name`),
  KEY `idx_data_type` (`data_type`),
  KEY `idx_is_primary_key` (`is_primary_key`),
  KEY `idx_is_deleted` (`is_deleted`),
  CONSTRAINT `fk_field_table` FOREIGN KEY (`table_id`) REFERENCES `table_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段信息表';

-- SQL解析历史表
CREATE TABLE IF NOT EXISTS `sql_parse_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `original_sql` text NOT NULL COMMENT '原始SQL语句',
  `sql_type` varchar(50) DEFAULT NULL COMMENT 'SQL类型',
  `parse_status` varchar(20) DEFAULT NULL COMMENT '解析状态',
  `parse_time` datetime DEFAULT NULL COMMENT '解析时间',
  `lineage_count` int(11) DEFAULT '0' COMMENT '血缘关系数量',
  `table_count` int(11) DEFAULT '0' COMMENT '涉及表数量',
  `datasource_count` int(11) DEFAULT '0' COMMENT '涉及数据源数量',
  `error_message` text COMMENT '错误信息',
  `warnings` json DEFAULT NULL COMMENT '警告信息',
  `metadata` json DEFAULT NULL COMMENT '元数据信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 (0: 未删除, 1: 已删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_sql_type` (`sql_type`),
  KEY `idx_parse_status` (`parse_status`),
  KEY `idx_parse_time` (`parse_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SQL解析历史表';

-- 插入一些示例数据源配置
INSERT INTO `datasource_info` (`source_key`, `source_type`, `source_address`, `source_name`, `database_name`, `status`, `create_by`, `remark`) VALUES
('mysql.localhost:3306', 'mysql', 'localhost:3306', 'MySQL本地数据库', 'test_db', 1, 'system', '本地MySQL测试数据库'),
('kafka.*************:9092', 'kafka', '*************:9092', 'Kafka集群', NULL, 1, 'system', 'Kafka消息队列'),
('postgresql.*************:5432', 'postgresql', '*************:5432', 'PostgreSQL数据库', 'analytics_db', 1, 'system', 'PostgreSQL分析数据库'),
('hive.*************:10000', 'hive', '*************:10000', 'Hive数据仓库', 'warehouse', 1, 'system', 'Hive数据仓库')
ON DUPLICATE KEY UPDATE 
  `source_name` = VALUES(`source_name`),
  `database_name` = VALUES(`database_name`),
  `update_time` = CURRENT_TIMESTAMP;
