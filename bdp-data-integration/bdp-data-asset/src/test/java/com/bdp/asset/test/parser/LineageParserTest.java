package com.bdp.asset.test.parser;

import com.bdp.asset.test.model.LineageResult;
import com.bdp.asset.test.parser.impl.*;
import com.bdp.asset.test.exception.SQLParseException;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 血缘解析器测试类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@SpringBootTest
public class LineageParserTest {
    
    private FlinkSqlParser flinkSqlParser;
    private MysqlSqlParser mysqlSqlParser;
    private CommonSQLParser commonSqlParser;
    
    @BeforeEach
    void setUp() {
        flinkSqlParser = new FlinkSqlParser();
        mysqlSqlParser = new MysqlSqlParser();
        commonSqlParser = new CommonSQLParser();
    }
    
    @Test
    void testFlinkSqlCreateTable() throws SQLParseException {
        String sql = """
            CREATE TABLE kafka_source (
                id BIGINT,
                name VARCHAR(255),
                age INT,
                create_time TIMESTAMP(3),
                WATERMARK FOR create_time AS create_time - INTERVAL '5' SECOND
            ) WITH (
                'connector' = 'kafka',
                'topic' = 'user_events',
                'properties.bootstrap.servers' = '*************:9092',
                'properties.group.id' = 'test_group',
                'format' = 'json'
            )
            """;
        
        LineageResult result = flinkSqlParser.parse(sql);
        
        assertNotNull(result);
        assertEquals("FLINK_SQL", result.getSqlType());
        assertTrue(result.isSuccess());
        assertTrue(result.getDataSourceCount() > 0);
        assertTrue(result.getTableCount() > 0);
        
        // 验证数据源信息
        assertEquals("kafka", result.getDataSources().get(0).getType());
        assertEquals("*************:9092", result.getDataSources().get(0).getAddress());
        assertEquals("kafka.*************:9092", result.getDataSources().get(0).getSourceKey());
    }
    
    @Test
    void testFlinkSqlInsertSelect() throws SQLParseException {
        String sql = """
            INSERT INTO sink_table
            SELECT id, name, age, create_time
            FROM source_table
            WHERE age > 18
            """;
        
        LineageResult result = flinkSqlParser.parse(sql);
        
        assertNotNull(result);
        assertEquals("FLINK_SQL", result.getSqlType());
        // 注意：由于当前实现还不完整，可能会有警告
    }
    
    @Test
    void testMysqlCreateTable() throws SQLParseException {
        String sql = """
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                email VARCHAR(100) UNIQUE,
                age INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """;
        
        LineageResult result = mysqlSqlParser.parse(sql);
        
        assertNotNull(result);
        assertEquals("MYSQL", result.getSqlType());
        assertTrue(result.isSuccess());
        assertTrue(result.getTableCount() > 0);
        
        // 验证表信息
        assertEquals("users", result.getTables().get(0).getTableName());
        assertTrue(result.getTables().get(0).getFields().size() > 0);
    }
    
    @Test
    void testMysqlInsertSelect() throws SQLParseException {
        String sql = """
            INSERT INTO target_users (id, name, email)
            SELECT user_id, user_name, user_email
            FROM source_users
            WHERE status = 'active'
            """;
        
        LineageResult result = mysqlSqlParser.parse(sql);
        
        assertNotNull(result);
        assertEquals("MYSQL", result.getSqlType());
        assertTrue(result.isSuccess());
        assertTrue(result.hasLineageRelations());
    }
    
    @Test
    void testCommonSqlSelect() throws SQLParseException {
        String sql = """
            SELECT 
                u.id,
                u.name,
                u.email,
                p.title as profile_title,
                UPPER(u.name) as upper_name,
                COUNT(*) as total_count
            FROM users u
            LEFT JOIN profiles p ON u.id = p.user_id
            WHERE u.status = 'active'
            GROUP BY u.id, u.name, u.email, p.title
            """;
        
        LineageResult result = commonSqlParser.parse(sql);
        
        assertNotNull(result);
        assertEquals("COMMON_SQL", result.getSqlType());
        assertTrue(result.isSuccess());
    }
    
    @Test
    void testInvalidSql() {
        String invalidSql = "SELECT * FROM";
        
        assertThrows(SQLParseException.class, () -> {
            commonSqlParser.parse(invalidSql);
        });
    }
    
    @Test
    void testEmptySql() {
        String emptySql = "";
        
        assertThrows(SQLParseException.class, () -> {
            commonSqlParser.parse(emptySql);
        });
    }
    
    @Test
    void testComplexFlinkSql() throws SQLParseException {
        String sql = """
            CREATE TABLE orders (
                order_id BIGINT,
                user_id BIGINT,
                product_id BIGINT,
                quantity INT,
                price DECIMAL(10,2),
                order_time TIMESTAMP(3),
                WATERMARK FOR order_time AS order_time - INTERVAL '1' MINUTE
            ) WITH (
                'connector' = 'kafka',
                'topic' = 'orders',
                'properties.bootstrap.servers' = '*************:9092',
                'format' = 'avro'
            );
            
            CREATE TABLE order_summary (
                user_id BIGINT,
                total_orders BIGINT,
                total_amount DECIMAL(10,2),
                window_start TIMESTAMP(3),
                window_end TIMESTAMP(3)
            ) WITH (
                'connector' = 'jdbc',
                'url' = '*****************************************',
                'table-name' = 'order_summary'
            );
            
            INSERT INTO order_summary
            SELECT 
                user_id,
                COUNT(*) as total_orders,
                SUM(quantity * price) as total_amount,
                TUMBLE_START(order_time, INTERVAL '1' HOUR) as window_start,
                TUMBLE_END(order_time, INTERVAL '1' HOUR) as window_end
            FROM orders
            GROUP BY user_id, TUMBLE(order_time, INTERVAL '1' HOUR);
            """;
        
        LineageResult result = flinkSqlParser.parse(sql);
        
        assertNotNull(result);
        assertEquals("FLINK_SQL", result.getSqlType());
        // 复杂SQL可能需要更完善的解析器才能完全处理
    }
}
