package com.bdp.asset.test.service;

import com.bdp.asset.test.entity.LineageEntity;
import com.bdp.asset.test.model.LineageResult;
import com.bdp.asset.test.exception.SQLParseException;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

/**
 * 血缘服务测试类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@SpringBootTest
@Transactional
public class LineageServiceTest {
    
    @Autowired
    private LineageService lineageService;
    
    @Test
    void testParseAndSaveFlinkSql() throws SQLParseException {
        String sql = """
            CREATE TABLE user_events (
                user_id BIGINT,
                event_type VARCHAR(50),
                event_time TIMESTAMP(3),
                WATERMARK FOR event_time AS event_time - INTERVAL '5' SECOND
            ) WITH (
                'connector' = 'kafka',
                'topic' = 'user_events',
                'properties.bootstrap.servers' = '192.168.1.200:9092',
                'format' = 'json'
            )
            """;
        
        LineageResult result = lineageService.parseAndSave(sql);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("FLINK_SQL", result.getSqlType());
    }
    
    @Test
    void testParseAndSaveMysqlSql() throws SQLParseException {
        String sql = """
            INSERT INTO target_table (id, name, email)
            SELECT user_id, user_name, user_email
            FROM source_table
            WHERE status = 'active'
            """;
        
        LineageResult result = lineageService.parseAndSave(sql, "MYSQL");
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("MYSQL", result.getSqlType());
        
        if (result.hasLineageRelations()) {
            Integer savedCount = (Integer) result.getMetadata().get("savedCount");
            assertNotNull(savedCount);
            assertTrue(savedCount > 0);
        }
    }
    
    @Test
    void testFindBySourceTable() throws SQLParseException {
        // 先插入一些测试数据
        String sql = """
            INSERT INTO users (name, email)
            SELECT customer_name, customer_email
            FROM customers
            WHERE active = 1
            """;
        
        lineageService.parseAndSave(sql, "MYSQL");
        
        // 查询血缘关系
        List<LineageEntity> lineages = lineageService.findBySourceTable("mysql.localhost:3306", "customers");
        
        assertNotNull(lineages);
        // 根据实际解析结果验证
    }
    
    @Test
    void testFindByTargetTable() throws SQLParseException {
        // 先插入一些测试数据
        String sql = """
            INSERT INTO analytics_summary
            SELECT user_id, COUNT(*) as total_events
            FROM user_events
            GROUP BY user_id
            """;
        
        lineageService.parseAndSave(sql, "COMMON_SQL");
        
        // 查询血缘关系
        List<LineageEntity> lineages = lineageService.findByTargetTable("unknown.unknown", "analytics_summary");
        
        assertNotNull(lineages);
    }
    
    @Test
    void testCountStatistics() {
        Long totalCount = lineageService.countTotal();
        assertNotNull(totalCount);
        assertTrue(totalCount >= 0);
        
        String[] supportedTypes = lineageService.getSupportedSqlTypes();
        assertNotNull(supportedTypes);
        assertTrue(supportedTypes.length > 0);
        
        for (String type : supportedTypes) {
            Long count = lineageService.countBySqlType(type);
            assertNotNull(count);
            assertTrue(count >= 0);
        }
    }
    
    @Test
    void testExistsLineage() {
        boolean exists = lineageService.existsLineage(
            "mysql.localhost:3306.test_db.source_table.field1",
            "mysql.localhost:3306.test_db.target_table.field1"
        );
        
        // 根据实际数据情况验证
        assertNotNull(exists);
    }
    
    @Test
    void testComplexSqlParsing() throws SQLParseException {
        String complexSql = """
            WITH user_stats AS (
                SELECT 
                    user_id,
                    COUNT(*) as event_count,
                    MAX(event_time) as last_event_time
                FROM user_events
                WHERE event_time >= CURRENT_DATE - INTERVAL '7' DAY
                GROUP BY user_id
            ),
            active_users AS (
                SELECT 
                    u.user_id,
                    u.user_name,
                    u.email,
                    s.event_count,
                    s.last_event_time
                FROM users u
                JOIN user_stats s ON u.user_id = s.user_id
                WHERE s.event_count > 10
            )
            INSERT INTO active_user_summary
            SELECT 
                user_id,
                user_name,
                email,
                event_count,
                last_event_time,
                CURRENT_TIMESTAMP as summary_time
            FROM active_users
            """;
        
        LineageResult result = lineageService.parseAndSave(complexSql);
        
        assertNotNull(result);
        // 复杂SQL可能需要更完善的解析器
        if (!result.isSuccess()) {
            assertNotNull(result.getErrorMessage());
        }
    }
    
    @Test
    void testInvalidSqlHandling() {
        String invalidSql = "INVALID SQL STATEMENT";
        
        assertThrows(SQLParseException.class, () -> {
            lineageService.parseAndSave(invalidSql);
        });
    }
    
    @Test
    void testMultipleSqlTypes() throws SQLParseException {
        // 测试不同类型的SQL
        String[] sqls = {
            "SELECT * FROM test_table",
            "CREATE TABLE test (id INT) ENGINE=InnoDB",
            "INSERT INTO target SELECT * FROM source"
        };
        
        for (String sql : sqls) {
            try {
                LineageResult result = lineageService.parseAndSave(sql);
                assertNotNull(result);
                assertNotNull(result.getSqlType());
            } catch (SQLParseException e) {
                // 某些SQL可能解析失败，这是正常的
                assertNotNull(e.getMessage());
            }
        }
    }
}
