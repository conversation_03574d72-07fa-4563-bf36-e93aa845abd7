#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# PR-943: Improve checkstyle and add import package rules
243d840fc5f0f46020ca3fe584606870b0b821bb
# PR-2213: added spotless-plugin and checkstyle improvement
39cc162be89fd861a88d3a385ee9e8db7b9564d8
# PR-2625: [Improve] spotless plugin support scala code format
dec2af903f9381e4875bd885edc8e9d5d7720c9b
