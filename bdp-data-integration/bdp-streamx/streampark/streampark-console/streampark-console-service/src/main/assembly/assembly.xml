<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<assembly>
    <id>bin</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <baseDirectory>streamx-console-service-${project.version}</baseDirectory>

    <dependencySets>
        <dependencySet>
            <unpack>false</unpack>
            <scope>runtime</scope>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>lib</outputDirectory>
            <excludes>
                <exclude>org.apache.streampark:streampark-flink-sqlclient_${scala.binary.version}</exclude>
                <exclude>javax.servlet:servlet-api</exclude>
            </excludes>
        </dependencySet>
        <dependencySet>
            <outputDirectory>client</outputDirectory>
            <includes>
                <include>org.apache.streampark:streampark-flink-sqlclient_${scala.binary.version}</include>
            </includes>
        </dependencySet>
    </dependencySets>

    <fileSets>
        <fileSet>
            <directory>${project.build.directory}/../../../.mvn</directory>
            <outputDirectory>bin/.mvn</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../src/main/assembly/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <lineEnding>unix</lineEnding>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/lib</directory>
            <outputDirectory>lib</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/shims</directory>
            <outputDirectory>lib</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../src/main/assembly/conf</directory>
            <outputDirectory>conf</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../src/main/assembly/logs</directory>
            <outputDirectory>logs</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../src/main/assembly/temp</directory>
            <outputDirectory>temp</outputDirectory>
            <fileMode>0777</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../src/main/assembly/client</directory>
            <outputDirectory>client</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../src/main/assembly/plugins</directory>
            <outputDirectory>plugins</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../src/main/assembly/script</directory>
            <outputDirectory>script</outputDirectory>
            <lineEnding>unix</lineEnding>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../src/main/resources</directory>
            <outputDirectory>conf</outputDirectory>
            <lineEnding>unix</lineEnding>
            <fileMode>0755</fileMode>
            <includes>
                <include>logback-spring.xml</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../../../</directory>
            <outputDirectory/>
            <includes>
                <include>README.md</include>
                <include>DISCLAIMER</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/../../../dist-material/release-docs</directory>
            <outputDirectory/>
        </fileSet>
    </fileSets>
</assembly>
