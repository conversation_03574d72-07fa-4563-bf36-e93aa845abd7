/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

use streampark;

SET NAMES utf8mb4;
SET foreign_key_checks = 0;

UPDATE `t_flink_app` a INNER JOIN `t_flink_cluster` c
ON a.`cluster_id` = c.`cluster_id`
AND a.`execution_mode` = 5
SET a.`flink_cluster_id` = c.`id`;

UPDATE `t_flink_app`
SET `cluster_id` = `app_id`
WHERE `execution_mode` IN (2,3,4);

ALTER TABLE `t_flink_app` DROP COLUMN `app_id`;

SET foreign_key_checks = 1;
