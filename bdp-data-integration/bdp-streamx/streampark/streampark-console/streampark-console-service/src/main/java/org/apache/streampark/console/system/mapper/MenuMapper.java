/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.streampark.console.system.mapper;

import org.apache.streampark.console.system.entity.Menu;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface MenuMapper extends BaseMapper<Menu> {

  List<String> findUserPermissions(@Param("userId") Long userId, @Param("teamId") Long teamId);

  List<Menu> findUserMenus(@Param("userId") Long userId, @Param("teamId") Long teamId);

  /**
   * Find the user ID associated with the current menu or button
   *
   * @param menuId menuId
   * @return user id list
   */
  List<String> findUserIdsByMenuId(String menuId);
}
