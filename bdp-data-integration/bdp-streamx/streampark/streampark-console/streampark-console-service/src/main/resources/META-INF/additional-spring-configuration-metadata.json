{"properties": [{"name": "streampark.shiro.anonUrl", "type": "java.lang.String", "description": "Description for streampark.shiro.anonUrl."}, {"name": "streampark.shiro.jwtTimeOut", "type": "java.lang.String", "description": "Description for streampark.shiro.jwtTimeOut."}, {"name": "streampark.hdfs.workspace", "type": "java.lang.String", "description": "Description for streampark.hdfs.workspace."}, {"name": "streampark.docker.register.image-namespace", "type": "java.lang.String", "description": "Description for streampark.docker.register.image-namespace."}, {"name": "streampark.flink-k8s.tracking.silent-state-keep-sec", "type": "java.lang.String", "description": "Description for streampark.flink-k8s.tracking.silent-state-keep-sec."}, {"name": "streampark.flink-k8s.tracking.polling-task-timeout-sec.job-status", "type": "java.lang.String", "description": "Description for streampark.flink-k8s.tracking.polling-task-timeout-sec.job-status."}, {"name": "streampark.flink-k8s.tracking.polling-task-timeout-sec.cluster-metric", "type": "java.lang.String", "description": "Description for streampark.flink-k8s.tracking.polling-task-timeout-sec.cluster-metric."}, {"name": "streampark.flink-k8s.tracking.polling-interval-sec.job-status", "type": "java.lang.String", "description": "Description for streampark.flink-k8s.tracking.polling-interval-sec.job-status."}, {"name": "streampark.flink-k8s.tracking.polling-interval-sec.cluster-metric", "type": "java.lang.String", "description": "Description for streampark.flink-k8s.tracking.polling-interval-sec.cluster-metric."}, {"name": "spring.devtools.restart.enabled", "type": "java.lang.String", "description": "Description for spring.devtools.restart.enabled."}, {"name": "streampark.workspace.local", "type": "java.lang.String", "description": "Description for streampark.workspace.local."}, {"name": "streampark.workspace.remote", "type": "java.lang.String", "description": "Description for streampark.workspace.remote."}]}