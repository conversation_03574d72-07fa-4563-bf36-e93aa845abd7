<!DOCTYPE html>
<html>
<head>
    <title>StreamPark Email</title>
    <link rel="shortcut icon"
          href="favicon.ico">
    <style type="text/css">
        table[name="blk_permission"], table[name="blk_footer"] {
            display: none;
        }
    </style>

    <meta name="googlebot" content="noindex"/>
    <META NAME="ROBOTS" CONTENT="NOINDEX, NOFOLLOW"/>

    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <style type="text/css">
        /*** BMEMBF Start ***/
        [name=bmeMainBody] {
            min-height: 1000px;
        }

        @media only screen and (max-width: 480px) {
            table.blk, table.tblText, .bmeHolder, .bmeHolder1, table.bmeMainColumn {
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeImageCard table.bmeCaptionTable td.tblCell {
                padding: 0px 20px 20px 20px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeImageCard table.bmeCaptionTable.bmeCaptionTableMobileTop td.tblCell {
                padding: 20px 20px 0 20px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table.bmeCaptionTable td.tblCell {
                padding: 10px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table.tblGtr {
                padding-bottom: 20px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            td.blk_container, .blk_parent, .bmeLeftColumn, .bmeRightColumn, .bmeColumn1, .bmeColumn2, .bmeColumn3, .bmeBody {
                display: table !important;
                max-width: 600px !important;
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table.container-table, .bmeheadertext, .container-table {
                width: 95% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .mobile-footer, .mobile-footer a {
                font-size: 13px !important;
                line-height: 18px !important;
            }

            .mobile-footer {
                text-align: center !important;
            }

            table.share-tbl {
                padding-bottom: 15px;
                width: 100% !important;
            }

            table.share-tbl td {
                display: block !important;
                text-align: center !important;
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            td.bmeShareTD, td.bmeSocialTD {
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            td.tdBoxedTextBorder {
                width: auto !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table.blk, table[name=tblText], .bmeHolder, .bmeHolder1, table[name=bmeMainColumn] {
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeImageCard table.bmeCaptionTable td[name=tblCell] {
                padding: 0px 20px 20px 20px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeImageCard table.bmeCaptionTable.bmeCaptionTableMobileTop td[name=tblCell] {
                padding: 20px 20px 0 20px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table.bmeCaptionTable td[name=tblCell] {
                padding: 10px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table[name=tblGtr] {
                padding-bottom: 20px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            td.blk_container, .blk_parent, [name=bmeLeftColumn], [name=bmeRightColumn], [name=bmeColumn1], [name=bmeColumn2], [name=bmeColumn3], [name=bmeBody] {
                display: table !important;
                max-width: 600px !important;
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table[class=container-table], .bmeheadertext, .container-table {
                width: 95% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .mobile-footer, .mobile-footer a {
                font-size: 13px !important;
                line-height: 18px !important;
            }

            .mobile-footer {
                text-align: center !important;
            }

            table[class="share-tbl"] {
                padding-bottom: 15px;
                width: 100% !important;
            }

            table[class="share-tbl"] td {
                display: block !important;
                text-align: center !important;
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            td[name=bmeShareTD], td[name=bmeSocialTD] {
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            td[name=tdBoxedTextBorder] {
                width: auto !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeImageCard table.bmeImageTable {
                height: auto !important;
                width: 100% !important;
                padding: 20px !important;
                clear: both;
                float: left !important;
                border-collapse: separate;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblInline table.bmeImageTable {
                height: auto !important;
                width: 100% !important;
                padding: 10px !important;
                clear: both;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblInline table.bmeCaptionTable {
                width: 100% !important;
                clear: both;
            }
        }

        @media only screen and (max-width: 480px) {
            table.bmeImageTable {
                height: auto !important;
                width: 100% !important;
                padding: 10px !important;
                clear: both;
            }
        }

        @media only screen and (max-width: 480px) {
            table.bmeCaptionTable {
                width: 100% !important;
                clear: both;
            }
        }

        @media only screen and (max-width: 480px) {
            table.bmeImageContainer {
                width: 100% !important;
                clear: both;
                float: left !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table.bmeImageTable td {
                padding: 0px !important;
                height: auto;
            }
        }

        @media only screen and (max-width: 480px) {
            td.bmeImageContainerRow {
                padding: 0px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            img.mobile-img-large {
                width: 100% !important;
                height: auto !important;
            }
        }

        @media only screen and (max-width: 480px) {
            img.bmeRSSImage {
                max-width: 320px;
                height: auto !important;
            }
        }

        @media only screen and (min-width: 640px) {
            img.bmeRSSImage {
                max-width: 600px !important;
                height: auto !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .trMargin img {
                height: 10px;
            }
        }

        @media only screen and (max-width: 480px) {
            div.bmefooter, div.bmeheader {
                display: block !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .tdPart {
                width: 100% !important;
                clear: both;
                float: left !important;
            }
        }

        @media only screen and (max-width: 480px) {
            table.blk_parent1, table.tblPart {
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .tblLine {
                min-width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblCenter img {
                margin: 0 auto;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblCenter, .bmeMblCenter div, .bmeMblCenter span {
                text-align: center !important;
                text-align: -webkit-center !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeNoBr br, .bmeImageGutterRow, .bmeMblStackCenter .bmeShareItem .tdMblHide {
                display: none !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblInline table.bmeImageTable, .bmeMblInline table.bmeCaptionTable, td.bmeMblInline {
                clear: none !important;
                width: 50% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblInlineHide, .bmeShareItem .trMargin {
                display: none !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblInline table.bmeImageTable img, .bmeMblShareCenter.tblContainer.mblSocialContain, .bmeMblFollowCenter.tblContainer.mblSocialContain {
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblStack > .bmeShareItem {
                width: 100% !important;
                clear: both !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeShareItem {
                padding-top: 10px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .tdPart.bmeMblStackCenter, .bmeMblStackCenter .bmeFollowItemIcon {
                padding: 0px !important;
                text-align: center !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblStackCenter > .bmeShareItem {
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            td.bmeMblCenter {
                border: 0 none transparent !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeLinkTable.tdPart td {
                padding-left: 0px !important;
                padding-right: 0px !important;
                border: 0 none transparent !important;
                padding-bottom: 15px !important;
                height: auto !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .tdMblHide {
                width: 10px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeShareItemBtn {
                display: table !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblStack td {
                text-align: left !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblStack .bmeFollowItem {
                clear: both !important;
                padding-top: 10px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblStackCenter .bmeFollowItemText {
                padding-left: 5px !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .bmeMblStackCenter .bmeFollowItem {
                clear: both !important;
                align-self: center;
                float: none !important;
                padding-top: 10px;
                margin: 0 auto;
            }
        }

        @media only screen and (max-width: 480px) {
            .tdPart > table {
                width: 100% !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .tdPart > table.bmeLinkContainer {
                width: auto !important;
            }
        }

        @media only screen and (max-width: 480px) {
            .tdPart.mblStackCenter > table.bmeLinkContainer {
                width: 100% !important;
            }
        }

        .blk_parent:first-child, .blk_parent {
            float: left;
        }

        .blk_parent:last-child {
            float: right;
        }

        /*** BMEMBF END ***/

        table[name="bmeMainBody"], body {
            background-color: #f5f7fb;
        }

        td[name="bmePreHeader"] {
            background-color: transparent;
        }

        td[name="bmeHeader"] {
            background: #ffffff;
            background-color: #1fc899;
        }

        td[name="bmeBody"], table[name="bmeBody"] {
            background-color: #ffffff;
        }

        td[name="bmePreFooter"] {
            background-color: #ffffff;
        }

        td[name="bmeFooter"] {
            background-color: transparent;
        }

        td[name="tblCell"], .blk {
            font-family: initial;
            font-weight: normal;
            font-size: initial;
        }

        table[name="blk_blank"] td[name="tblCell"] {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 1em;
        }

        [name=bmeMainContentParent] {
            border-color: #808080;
            border-width: 0px;
            border-style: none;
            border-radius: 0px;
            border-collapse: separate;
            border-spacing: 0px;
            overflow: hidden;
        }

        [name=bmeMainColumnParent] {
            border-color: transparent;
            border-width: 0px;
            border-style: none;
            border-radius: 0px;
        }

        [name=bmeMainColumn] {
            border-color: transparent;
            border-width: 0px;
            border-style: none;
            border-radius: 0px;
            border-collapse: separate;
            border-spacing: 0px;
        }

        [name=bmeMainContent] {
            border-color: transparent;
            border-width: 0px;
            border-style: none;
            border-radius: 0px;
            border-collapse: separate;
            border-spacing: 0px;
        }


        .detail-table {
            border-collapse: collapse;
            margin-top: 24px;
            margin-bottom: 24px;
        }

        .detail-table tr td {
            border: 1px solid #d0d0d0;
            padding: .55em;
        }

        .detail-table tr td:first-child {
            font-weight: bold;
        }

        .detail-table tr td:last-child {
            font-weight: bold;
        }

    </style>
</head>
<body marginheight=0 marginwidth=0 topmargin=0 leftmargin=0
      style="height: 100% !important; margin: 0; padding: 0; width: 100% !important;min-width: 100%;">

<table width="100%"
       cellspacing="0"
       cellpadding="0"
       border="0"
       name="bmeMainBody"
       bgcolor="#f5f7fb">
    <tbody>
    <tr>
        <td width="100%"
            valign="top"
            align="center">
            <table cellspacing="0"
                   cellpadding="0"
                   border="0"
                   name="bmeMainColumnParentTable">
                <tbody>
                <tr>
                    <td name="bmeMainColumnParent"
                        style="border-collapse: separate; border: 0 none transparent; border-radius: 0px;">
                        <table name="bmeMainColumn"
                               class="bmeHolder bmeMainColumn"
                               style="max-width: 600px; border-image: initial; border-radius: 0px; border-collapse: separate; border-spacing: 0px; overflow: visible;"
                               cellspacing="0"
                               cellpadding="0"
                               border="0"
                               align="center">
                            <tbody>
                            <tr>
                                <td width="100%"
                                    class="blk_container bmeHolder"
                                    name="bmePreHeader"
                                    valign="top"
                                    align="center"
                                    style="color: rgb(102, 102, 102); border: 0 none transparent;"
                                    bgcolor="">
                                </td>
                            </tr>

                            <tr>
                                <td width="100%"
                                    class="bmeHolder"
                                    valign="top"
                                    align="center"
                                    name="bmeMainContentParent"
                                    style="border: 0 none rgb(128, 128, 128); border-radius: 0px; border-collapse: separate; border-spacing: 0px; overflow: hidden;">
                                    <table name="bmeMainContent"
                                           width="100%"
                                           cellspacing="0"
                                           cellpadding="0"
                                           border="0"
                                           align="center"
                                           style="font-family: Open Sans,-apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
                                            border-collapse: collapse;
                                            width: 100%;
                                            border-radius: 3px;
                                            -webkit-box-shadow: 0 1px 4px rgb(0 0 0 / 5%);
                                            box-shadow: 0 1px 4px rgb(0 0 0 / 5%);
                                            border: 2px solid #f0f0f0;">
                                        <tbody>
                                        <tr>
                                            <td width="100%"
                                                class="blk_container bmeHolder"
                                                name="bmeHeader"
                                                valign="top"
                                                align="center"
                                                style="border: 0 none transparent; background-color: rgb(30,30,30); color: rgb(222,222,222);"
                                                bgcolor="#1fc899">
                                                <div id="dv_11"
                                                     class="blk_wrapper"
                                                >
                                                    <table width="600"
                                                           cellspacing="0"
                                                           cellpadding="0"
                                                           border="0"
                                                           class="blk"
                                                           name="blk_divider"
                                                    >
                                                        <tbody>
                                                        <tr>
                                                            <td class="tblCellMain"
                                                                style="padding: 5px 20px;">
                                                                <table class="tblLine"
                                                                       cellspacing="0"
                                                                       cellpadding="0"
                                                                       border="0"
                                                                       width="100%"
                                                                       style="border-top-width: 0px; border-top-style: none; min-width: 1px;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td><span></span></td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div id="dv_18"
                                                     class="blk_wrapper"
                                                >
                                                    <table width="600"
                                                           cellspacing="0"
                                                           cellpadding="0"
                                                           border="0"
                                                           class="blk"
                                                           name="blk_divider"
                                                    >
                                                        <tbody>
                                                        <tr>
                                                            <td class="tblCellMain"
                                                                style="padding: 10px 20px;">
                                                                <table class="tblLine"
                                                                       cellspacing="0"
                                                                       cellpadding="0"
                                                                       border="0"
                                                                       width="100%"
                                                                       style="border-top-width: 0px; border-top-style: none; min-width: 1px;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td><span></span></td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div id="dv_1"
                                                     class="blk_wrapper"
                                                >
                                                    <table width="600"
                                                           cellspacing="0"
                                                           cellpadding="0"
                                                           border="0"
                                                           class="blk"
                                                           name="blk_text">
                                                        <tbody>
                                                        <tr>
                                                            <td>
                                                                <table cellpadding="0"
                                                                       cellspacing="0"
                                                                       border="0"
                                                                       width="100%"
                                                                       class="bmeContainerRow">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td class="tdPart"
                                                                            valign="top"
                                                                            align="center">
                                                                            <table cellspacing="0"
                                                                                   cellpadding="0"
                                                                                   border="0"
                                                                                   width="600"
                                                                                   name="tblText"
                                                                                   style="float:left; background-color:transparent;"
                                                                                   align="left"
                                                                                   class="tblText">
                                                                                <tbody>
                                                                                <tr>
                                                                                    <td valign="top"
                                                                                        align="left"
                                                                                        name="tblCell"
                                                                                        style="padding: 5px 20px; font-family: Arial, Helvetica, sans-serif; font-size: 1em; font-weight: 400; color: rgb(222,222,222); text-align: left;"
                                                                                        class="tblCell">
                                                                                        <div style="line-height: 125%; text-align: center;">
                                                                                            <img src="https://streampark.apache.org/image/logo_name_white.png"
                                                                                                 alt=""
                                                                                                 height="150px"
                                                                                                 class="img-illustration"
                                                                                                 style="height: 150px"/>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div id="dv_15"
                                                     class="blk_wrapper"
                                                >
                                                    <table width="600"
                                                           cellspacing="0"
                                                           cellpadding="0"
                                                           border="0"
                                                           class="blk"
                                                           name="blk_text">
                                                        <tbody>
                                                        <tr>
                                                            <td>
                                                                <table cellpadding="0"
                                                                       cellspacing="0"
                                                                       border="0"
                                                                       width="100%"
                                                                       class="bmeContainerRow">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td class="tdPart"
                                                                            valign="top"
                                                                            align="center">
                                                                            <table cellspacing="0"
                                                                                   cellpadding="0"
                                                                                   border="0"
                                                                                   width="600"
                                                                                   name="tblText"
                                                                                   style="float:left; background-color:transparent;"
                                                                                   align="left"
                                                                                   class="tblText">
                                                                                <tbody>
                                                                                <tr>
                                                                                    <td valign="top"
                                                                                        align="left"
                                                                                        name="tblCell"
                                                                                        style="padding: 5px 20px; font-family: Arial, Helvetica, sans-serif; font-size: 1em; font-weight: 400; color: rgb(222,222,222); text-align: left;"
                                                                                        class="tblCell">
                                                                                        <div style="line-height: 150%; text-align: center;">
                                                                                            <span style="font-size: 1rem; font-family: Tahoma, Arial, Helvetica, sans-serif; color: #f0f0f0; line-height: 150%;">Apache StreamPark , Make stream processing easier!</span>
                                                                                        </div>
                                                                                        <br>
                                                                                        <div style="line-height: 150%; text-align: center;">
                                                                                            <span style="font-size: 1.5rem; font-family: Tahoma, Arial, Helvetica, sans-serif; color: #f0f0f0; line-height: 150%;">${mail.title}</span>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div id="dv_19"
                                                     class="blk_wrapper">
                                                    <table width="600"
                                                           cellspacing="0"
                                                           cellpadding="0"
                                                           border="0"
                                                           class="blk"
                                                           name="blk_divider"
                                                    >
                                                        <tbody>
                                                        <tr>
                                                            <td class="tblCellMain"
                                                                style="padding-top:20px; padding-bottom:20px;padding-left:20px;padding-right:20px;">
                                                                <table class="tblLine"
                                                                       cellspacing="0"
                                                                       cellpadding="0"
                                                                       border="0"
                                                                       width="100%"
                                                                       style="border-top-width: 0px; border-top-style: none; min-width: 1px;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td><span></span></td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td width="100%">
                                                <table class="bmeHolder"
                                                       name="bmeBody"
                                                       cellspacing="0"
                                                       cellpadding="0"
                                                       border="0"
                                                       align="center"
                                                       width="100%"
                                                       style="color: rgb(222,222,222); border: 0 none transparent; background-color: rgb(30,45,75);">
                                                    <tbody>
                                                    <tr>
                                                        <td width="100%"
                                                            valign="top"
                                                            align="center">
                                                            <table class="blk_parent1"
                                                                    cellspacing="0"
                                                                    cellpadding="0"
                                                                    style="font-weight: bold"
                                                                    border="0px"
                                                                    width="550px">
                                                                <tbody>
                                                                <tr>
                                                                    <td colspan="2">
                                                                        <br>
                                                                        <p style="margin: 0 0 1em;">
                                                                            Dear StreamPark user:
                                                                        </p>
                                                                        <p style="margin: 0 1em 1em;">
                                                                            Oops! I'm sorry to inform you that something wrong with your app
                                                                        </p>
                                                                        <br>
                                                                    </td>
                                                                </tr>

                                                                <#if  mail.type == 1 >
                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            Job Name
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            ${mail.jobName}
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            Job Status
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            <span style="font-size: 1.25em;font-weight: bold;color: RED;">${mail.status}</span>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            Start Time
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            ${mail.startTime}
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            End Time
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            ${mail.endTime}
                                                                        </td>
                                                                    </tr>

                                                                    <#if  mail.restart >
                                                                        <tr>
                                                                            <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                                Duration
                                                                            </td>
                                                                            <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                                ${mail.duration}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td style="border-bottom-style:solid; border-bottom-width: 1px; border-bottom-color: rgba(169,169,169,.5); border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                                Restart
                                                                            </td>
                                                                            <td style="border: 1px solid rgba(169,169,169,.5); padding: 1em">
                                                                                <span style="color: red">${mail.restartIndex}</span>
                                                                                /
                                                                                ${mail.totalRestart}
                                                                            </td>
                                                                        </tr>
                                                                    <#else>
                                                                        <tr>
                                                                            <td style="border-bottom-style:solid; border-bottom-width: 1px; border-bottom-color: rgba(169,169,169,.5); border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                                Duration
                                                                            </td>
                                                                            <td style="border: 1px solid rgba(169,169,169,.5); padding: 1em">
                                                                                ${mail.duration}
                                                                            </td>
                                                                        </tr>
                                                                    </#if>
                                                                </#if>

                                                                <#if  mail.type == 2 >
                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            Job Name
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            ${mail.jobName}
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            CheckPoint Status
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            <span style="font-size: 1.25em;font-weight: bold;color: RED;">FAILED</span>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            Checkpoint Failure Rate Interval
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            <span style="color: red">${mail.cpFailureRateInterval}</span>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            Max Failures Per Interval
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            <span style="color: red">${mail.cpMaxFailureInterval}</span>
                                                                        </td>
                                                                    </tr>

                                                                    <tr>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            Start Time
                                                                        </td>
                                                                        <td style="border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5);border-right-style:solid; border-right-width: 1px; border-right-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            ${mail.startTime}
                                                                        </td>
                                                                    </tr>

                                                                    <tr>
                                                                        <td style="border-bottom-style:solid; border-bottom-width: 1px; border-bottom-color: rgba(169,169,169,.5); border-left-style:solid; border-left-width: 1px; border-left-color: rgba(169,169,169,.5); border-top-style:solid; border-top-width: 1px; border-top-color: rgba(169,169,169,.5); padding: 1em">
                                                                            Duration
                                                                        </td>
                                                                        <td style="border: 1px solid rgba(169,169,169,.5); padding: 1em">
                                                                            ${mail.duration}
                                                                        </td>
                                                                    </tr>
                                                                </#if>

                                                                <tr>
                                                                    <td colspan="2" style="height: 3rem;">
                                                                        <div style="margin: 1.5em 0;">Best Wishes!
                                                                        </div>
                                                                        <div style="margin: 1.5em 0;font-weight: bold;">
                                                                            StreamPark Team
                                                                        </div>
                                                                        <br>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="2">
                                                                        <table class="bmeHolder"
                                                                               cellspacing="0"
                                                                               cellpadding="0"
                                                                               border="0"
                                                                               align="center"
                                                                               width="100%">
                                                                            <tbody>
                                                                            <tr>
                                                                                <#if mail.link??>
                                                                                    <td style="border: 0 none transparent; text-align: center; font-family: Arial, Helvetica, sans-serif; font-size: 1em; padding: 15px 40px; font-weight: bold; background-color: rgb(243, 156, 18);"
                                                                                        class="bmeButtonText">
                                                                                            <span style="font-family: Tahoma, Arial, Helvetica, sans-serif; font-size: 1em; color: rgb(255, 255, 255);">
                                                                                                <a href="${mail.link}"
                                                                                                   style="color:#FFFFFF;text-decoration:none;"
                                                                                                   target="_blank">Details</a>
                                                                                            </span>
                                                                                    </td>
                                                                                    <td width="10px"></td>
                                                                                </#if>

                                                                                <td style="border: 0 none transparent; text-align: center; font-family: Arial, Helvetica, sans-serif; font-size: 1em; padding: 15px 40px; font-weight: bold; background-color: #1890ff;"
                                                                                    class="bmeButtonText">
                                                                                            <span style="font-family: Tahoma, Arial, Helvetica, sans-serif; font-size: 1em; color: rgb(255, 255, 255);">
                                                                                                <a href="https://streampark.apache.org"
                                                                                                   style="color:#FFFFFF;text-decoration:none;"
                                                                                                   target="_blank">Website</a>
                                                                                            </span>
                                                                                </td>
                                                                                <td width="10px"></td>

                                                                                <td style="border: 3px solid rgb(0, 174, 84); text-align: center; font-family: Arial, Helvetica, sans-serif; font-size: 1em; padding: 15px 30px; font-weight: bold; border-collapse: separate; background-color: rgb(0, 174, 84);"
                                                                                    class="bmeButtonText">
                                                                                            <span style="font-family: Tahoma, Arial, Helvetica, sans-serif; font-size: 1em; color: rgb(255, 255, 255);">
                                                                                                <a href="https://streampark.apache.org"
                                                                                                   style="color:#FFFFFF;text-decoration:none;">GitHub</a>
                                                                                            </span>
                                                                                </td>
                                                                            </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td width="100%"
                                                class="blk_container bmeHolder"
                                                name="bmePreFooter"
                                                valign="top"
                                                align="center"
                                                style="border: 0 none transparent; background-color: rgb(30,45,75);margin-top: -5px">
                                                <div id="dv_10"
                                                     class="blk_wrapper"
                                                     style="margin-top: 2rem">
                                                </div>

                                                <div id="dv_14"
                                                     class="blk_wrapper">
                                                    <table cellspacing="0"
                                                           cellpadding="0"
                                                           border="0"
                                                           style="background-color: rgb(30,30,30);"
                                                           name="blk_social_follow"
                                                           width="600"
                                                           class="blk">
                                                        <tbody>
                                                        <tr>
                                                            <td class="tblCellMain"
                                                                align="center"
                                                                style="padding-top:20px; padding-bottom:20px; padding-left:20px; padding-right:20px;">
                                                                <table class="tblContainer mblSocialContain"
                                                                       cellspacing="0"
                                                                       cellpadding="0"
                                                                       border="0"
                                                                       align="center"
                                                                       style="text-align:center;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td class="tdItemContainer"
                                                                        >
                                                                            <table cellspacing="0"
                                                                                   cellpadding="0"
                                                                                   border="0"
                                                                                   class="mblSocialContain"
                                                                                   style="table-layout: auto;">
                                                                                <tbody>
                                                                                <tr>
                                                                                    <td valign="top"
                                                                                        name="bmeSocialTD"
                                                                                        class="bmeSocialTD">
                                                                                        <!--[if gte mso 6]></td>

                                                                                    <td align="left"
                                                                                        valign="top">
                                                                                        <![endif]-->
                                                                                        <table cellspacing="0"
                                                                                               cellpadding="0"
                                                                                               border="0"
                                                                                               class="bmeFollowItem"
                                                                                               type="email"
                                                                                               style="float: left; display: block;"
                                                                                               align="left">
                                                                                            <tbody>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <span style="font-size: .75em; font-family: Tahoma, Arial, Helvetica, sans-serif; color: #999; margin-right: 10px;">Apache LICENSE 2.0 Licensed</span>
                                                                                                </td>
                                                                                                <td>
                                                                                                    <span style="font-size: .75em; font-family: Tahoma, Arial, Helvetica, sans-serif; color: #999; margin-right: 10px;">Copyright © 2019-2022 The Apache StreamPark Project</span>
                                                                                                </td>
                                                                                                <td align="left"
                                                                                                    class="bmeFollowItemIcon"
                                                                                                    gutter="20"
                                                                                                    width="24"
                                                                                                    style="height:20px;">
                                                                                                    <a href="mailto:<EMAIL>"
                                                                                                       style="display: inline-block;background-color: rgba(0, 0, 0, 0);border-radius: 28px;border-style: solid; border-width: 2px;margin-right: 10px; border-color: #999;"
                                                                                                       target="_blank"><img
                                                                                                            src="https://ui.benchmarkemail.com/images/editor/socialicons/em_btn.png"
                                                                                                            alt="Email"
                                                                                                            style="display: block; max-width: 114px;"
                                                                                                            border="0"
                                                                                                            width="24"
                                                                                                            height="24"></a>
                                                                                                </td>
                                                                                            </tr>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
    </tbody>
</table>
</body>
</html>
