<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.apache.streampark.console.core.mapper.ApplicationConfigMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.ApplicationConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="format" jdbcType="INTEGER" property="format"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="latest" jdbcType="BOOLEAN" property="latest"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
    </resultMap>

    <select id="getLatest" resultType="org.apache.streampark.console.core.entity.ApplicationConfig" parameterType="java.lang.Long">
        select *
        from t_flink_config
        where app_id=#{appId}
        and latest=true
    </select>

    <select id="getLastVersion" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select max(`version`) as lastVersion
        from t_flink_config
        where app_id = #{appId}
    </select>

    <select id="getEffective" resultType="org.apache.streampark.console.core.entity.ApplicationConfig" parameterType="java.lang.Long">
        select s.*
        from t_flink_config s
        inner join t_flink_effective e
        on s.id = e.target_id
        where e.app_id = #{appId}
        and e.target_type = 1
    </select>

    <select id="pageByAppId" resultType="org.apache.streampark.console.core.entity.ApplicationConfig" parameterType="java.lang.Long">
        select * from t_flink_config
        where app_id=#{appId}
    </select>

</mapper>
