<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.apache.streampark.console.core.mapper.ApplicationLogMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.ApplicationLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="job_id" jdbcType="BIGINT" property="jobId"/>
        <result column="yarn_app_id" jdbcType="VARCHAR" property="yarnAppId"/>
        <result column="job_manager_url" jdbcType="VARCHAR" property="jobManagerUrl"/>
        <result column="success" jdbcType="INTEGER" property="success"/>
        <result column="exception" jdbcType="LONGVARCHAR" property="exception"/>
        <result column="option_time" jdbcType="DATE" property="optionTime"/>
        <result column="option_name" jdbcType="INTEGER" property="optionName"/>
    </resultMap>

</mapper>
