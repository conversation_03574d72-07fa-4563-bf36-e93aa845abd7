<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.core.mapper.ApplicationMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.Application">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="module" jdbcType="VARCHAR" property="module"/>
        <result column="args" jdbcType="LONGVARCHAR" property="args"/>
        <result column="options" jdbcType="LONGVARCHAR" property="options"/>
        <result column="dynamic_properties" jdbcType="LONGVARCHAR" property="dynamicProperties"/>
        <result column="hot_params" jdbcType="VARCHAR" property="hotParams"/>
        <result column="job_name" jdbcType="VARCHAR" property="jobName"/>
        <result column="version_id" jdbcType="BIGINT" property="versionId"/>
        <result column="cluster_id" jdbcType="VARCHAR" property="clusterId"/>
        <result column="flink_cluster_id" jdbcType="BIGINT" property="flinkClusterId"/>
        <result column="flink_image" jdbcType="VARCHAR" property="flinkImage"/>
        <result column="k8s_name" jdbcType="VARCHAR" property="k8sName"/>
        <result column="k8s_namespace" jdbcType="VARCHAR" property="k8sNamespace"/>
        <result column="app_type" jdbcType="INTEGER" property="appType"/>
        <result column="job_type" jdbcType="INTEGER" property="jobType"/>
        <result column="resource_from" jdbcType="INTEGER" property="resourceFrom"/>
        <result column="execution_mode" jdbcType="INTEGER" property="executionMode"/>
        <result column="tracking" jdbcType="INTEGER" property="tracking"/>
        <result column="jar" jdbcType="VARCHAR" property="jar"/>
        <result column="jar_check_sum" jdbcType="VARCHAR" property="jarCheckSum"/>
        <result column="main_class" jdbcType="VARCHAR" property="mainClass"/>
        <result column="job_id" jdbcType="VARCHAR" property="jobId"/>
        <result column="job_manager_url" jdbcType="VARCHAR" property="jobManagerUrl"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="start_time" jdbcType="DATE" property="startTime"/>
        <result column="end_time" jdbcType="DATE" property="endTime"/>
        <result column="duration" jdbcType="BIGINT" property="duration"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="cp_max_failure_interval" jdbcType="INTEGER" property="cpMaxFailureInterval"/>
        <result column="cp_failure_rate_interval" jdbcType="INTEGER" property="cpFailureRateInterval"/>
        <result column="cp_failure_action" jdbcType="INTEGER" property="cpFailureAction"/>
        <result column="restart_size" jdbcType="INTEGER" property="restartSize"/>
        <result column="restart_count" jdbcType="INTEGER" property="restartCount"/>
        <result column="release" jdbcType="INTEGER" property="release"/>
        <result column="build" jdbcType="BOOLEAN" property="build"/>
        <result column="resolve_order" jdbcType="INTEGER" property="resolveOrder"/>
        <result column="total_tm" jdbcType="INTEGER" property="totalTM"/>
        <result column="total_slot" jdbcType="INTEGER" property="totalSlot"/>
        <result column="available_slot" jdbcType="INTEGER" property="availableSlot"/>
        <result column="total_task" jdbcType="INTEGER" property="totalTask"/>
        <result column="jm_memory" jdbcType="INTEGER" property="jmMemory"/>
        <result column="tm_memory" jdbcType="INTEGER" property="tmMemory"/>
        <result column="option_state" jdbcType="INTEGER" property="optionState"/>
        <result column="alert_id" jdbcType="BIGINT" property="alertId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
        <result column="option_time" jdbcType="DATE" property="optionTime"/>
        <result column="k8s_rest_exposed_type" jdbcType="INTEGER" property="k8sRestExposedType"/>
        <result column="k8s_pod_template" jdbcType="LONGVARCHAR" property="k8sPodTemplate"/>
        <result column="k8s_jm_pod_template" jdbcType="LONGVARCHAR" property="k8sJmPodTemplate"/>
        <result column="k8s_tm_pod_template" jdbcType="LONGVARCHAR" property="k8sTmPodTemplate"/>
        <result column="k8s_hadoop_integration" jdbcType="TINYINT" property="k8sHadoopIntegration"/>
        <result column="rest_url" jdbcType="VARCHAR" property="restUrl"/>
        <result column="rest_port" jdbcType="INTEGER" property="restPort"/>
        <result column="tags" jdbcType="VARCHAR" property="tags"/>
    </resultMap>

    <update id="resetOptionState">
        update t_flink_app
        set option_state = 0
    </update>

    <select id="existsRunningJobByClusterId" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        select
            CASE
                WHEN  count(1) > 0 THEN true ELSE false
            END
        from t_flink_app
            where flink_cluster_id = #{clusterId}
             and state = 5
             limit 1
    </select>

    <select id="countAffectedByClusterId" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select
            count(1)
        from t_flink_app
        where
            flink_cluster_id = #{clusterId}
            and state in (5, 7, 9)
            <choose>
                <when test="dbType == 'pgsql'">
                    and (end_time IS NULL or EXTRACT(EPOCH FROM (NOW() - end_time)) &lt;= 2)
                </when>
                <when test="dbType == 'mysql' or dbType == 'h2'">
                    and (end_time IS NULL or TIMESTAMPDIFF(SECOND, end_time, NOW()) &lt;= 2)
                </when>
            </choose>
            limit 1
    </select>

    <select id="selectAppsByProjectId" resultType="org.apache.streampark.console.core.entity.Application" parameterType="java.lang.Long">
        select * from t_flink_app where project_id=#{projectId}
    </select>

    <select id="selectPage" resultType="org.apache.streampark.console.core.entity.Application" parameterType="org.apache.streampark.console.core.entity.Application">
        select
        t.*,
        p.name as projectName,
--         u.username,
--         case
--         when trim(u.nick_name) = ''
--         then u.username
--         else u.nick_name
--         end as nickname,
        v.version as flinkVersion
        from t_flink_app t
--         inner join t_user u
--         on t.user_id = u.user_id
        left join t_flink_env v
        on t.version_id = v.id
        left join t_flink_project p
        on t.project_id = p.id
        <where>
            t.team_id = #{app.teamId}
            <if test="app.jobType != null and app.jobType != ''">
                and t.job_type = #{app.jobType}
            </if>
            <if test="app.jobTypeArray != null and app.jobTypeArray.length>0">
                and t.job_type in
                <foreach item="item" index="index" collection="app.jobTypeArray" open="("  close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="app.executionMode != null and app.executionMode != ''">
                and t.execution_mode = #{app.executionMode}
            </if>
            <if test="app.jobName != null and app.jobName != ''">
                <if test="_databaseId == 'mysql'">
                    and t.job_name like concat('%', #{app.jobName},'%')
                </if>
                <if test="_databaseId == 'pgsql'">
                    and t.job_name like '%' || #{app.jobName} || '%'
                </if>
            </if>
            <if test="app.projectName != null and app.projectName != ''">
                <if test="_databaseId == 'mysql'">
                    and p.name like concat('%', #{app.projectName},'%')
                </if>
                <if test="_databaseId == 'pgsql'">
                    and p.name like '%' || #{app.projectName} || '%'
                </if>
            </if>
            <if test="app.state != null and app.state != ''">
                and t.state = #{app.state}
            </if>
            <if test="app.userId != null and app.userId != ''">
                and t.user_id = #{app.userId}
            </if>
            <if test="app.stateArray != null and app.stateArray.length>0">
                and t.state in
                <foreach item="item" index="index" collection="app.stateArray" open="("  close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="app.tags != null and app.tags != ''">
                <if test="_databaseId == 'mysql'">
                    and t.tags like concat('%', #{app.tags},'%')
                </if>
                <if test="_databaseId == 'pgsql'">
                    and t.tags like '%' || #{app.tags} || '%'
                </if>
            </if>
        </where>
    </select>

    <select id="selectApp" resultType="org.apache.streampark.console.core.entity.Application" parameterType="long">
        select t.*, p.name as projectName
        from t_flink_app t left join t_flink_project p
        on t.project_id = p.id
        where t.id = #{id}
    </select>

    <update id="persistMetrics" parameterType="org.apache.streampark.console.core.entity.Application">
        update t_flink_app
        <set>
            <if test="app.jobId != null and app.jobId != ''">
                job_id=#{app.jobId},
            </if>
            <if test="app.tracking != null">
                tracking=#{app.tracking},
            </if>
            <if test="app.optionState != null">
                option_state=#{app.optionState},
            </if>
            <if test="app.startTime != null">
                start_time=#{app.startTime},
            </if>
            <if test="app.endTime != null">
                end_time=#{app.endTime},
            </if>
            <if test="app.duration != null">
                duration=#{app.duration},
            </if>
            <choose>
                <when test="@org.apache.streampark.console.core.enums.FlinkAppStateEnum@isEndState(app.state)">
                    total_tm=null,
                    total_slot=null,
                    total_task=null,
                    available_slot=null,
                    jm_memory=null,
                    tm_memory=null,
                </when>
                <otherwise>
                    <if test="app.totalTM != null">
                        total_tm=#{app.totalTM},
                    </if>
                    <if test="app.totalSlot != null">
                        total_slot=#{app.totalSlot},
                    </if>
                    <if test="app.totalTask != null">
                        total_task=#{app.totalTask},
                    </if>
                    <if test="app.availableSlot != null">
                        available_slot=#{app.availableSlot},
                    </if>
                    <if test="app.jmMemory != null">
                        jm_memory=#{app.jmMemory},
                    </if>
                    <if test="app.tmMemory != null">
                        tm_memory=#{app.tmMemory},
                    </if>
                </otherwise>
            </choose>
            state=#{app.state}
        </set>
        where id=#{app.id}
    </update>

    <select id="selectAppsByTeamId" resultType="org.apache.streampark.console.core.entity.Application" parameterType="java.lang.Long">
        select
            t.*,
            u.username,
            case
                when trim(u.nick_name) = ''
                    then u.username
                else u.nick_name
            end as nick_name
        from t_flink_app t
        inner join t_user u
        on t.user_id = u.user_id
        where t.team_id=#{teamId}
    </select>

    <update id="mapping" parameterType="org.apache.streampark.console.core.entity.Application">
        update t_flink_app
        <set>
            <if test="app.clusterId != null">
                cluster_id=#{app.clusterId},
            </if>
            <if test="app.jobId != null">
                job_id=#{app.jobId},
            </if>
            end_time=null,
            state=14,
            tracking=1
        </set>
        where id=#{app.id}
    </update>

    <select id="selectRecentK8sNamespaces" resultType="java.lang.String" parameterType="java.lang.Integer">
        select k8s_namespace
        from (
            select k8s_namespace, max(create_time) as ct
            from t_flink_app
            where k8s_namespace is not null
            group by k8s_namespace
            order by ct desc
        ) as ns
        limit #{limitSize}
    </select>

    <select id="selectRecentK8sClusterIds" resultType="java.lang.String" parameterType="java.util.Map">
        select cluster_id
        from (
            select cluster_id, max(create_time) as ct
            from t_flink_app
            where cluster_id is not null
            and execution_mode = #{executionMode}
            group by cluster_id
            order by ct desc
        ) as ci
        limit #{limitSize}
    </select>

    <select id="selectRecentFlinkBaseImages" resultType="java.lang.String" parameterType="java.lang.Integer">
        select flink_image
        from (
            select flink_image, max(create_time) as ct
            from t_flink_app
            where flink_image is not null
            and execution_mode = 6
            group by flink_image
            order by ct desc
        ) as fi
        limit #{limitSize}
    </select>

    <select id="selectRecentK8sPodTemplates" resultType="java.lang.String" parameterType="java.lang.Integer">
        select k8s_pod_template
        from (
            select k8s_pod_template, max(create_time) as ct
            from t_flink_app
            where k8s_pod_template is not null
            and k8s_pod_template !=''
            and execution_mode = 6
            group by k8s_pod_template
            order by ct desc
        ) as pt
        limit #{limitSize}
    </select>

    <select id="selectRecentK8sJmPodTemplates" resultType="java.lang.String" parameterType="java.lang.Integer">
        select k8s_jm_pod_template
        from (
            select k8s_jm_pod_template, max(create_time) as ct
            from t_flink_app
            where k8s_jm_pod_template is not null
            and k8s_jm_pod_template != ''
            and execution_mode = 6
            group by k8s_jm_pod_template
            order by ct desc
        ) as pt
        limit #{limitSize}
    </select>

    <select id="selectRecentK8sTmPodTemplates" resultType="java.lang.String" parameterType="java.lang.Integer">
        select k8s_tm_pod_template
        from (
            select k8s_tm_pod_template, max(create_time) as ct
            from t_flink_app
            where k8s_tm_pod_template is not null
            and k8s_tm_pod_template != ''
            and execution_mode = 6
            group by k8s_tm_pod_template
            order by ct desc
        ) as pt
        limit #{limitSize}
    </select>

</mapper>
