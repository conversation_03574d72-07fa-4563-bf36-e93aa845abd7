<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.core.mapper.EffectiveMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.Effective">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="target_type" jdbcType="BIGINT" property="targetType"/>
        <result column="target_id" jdbcType="BIGINT" property="targetId"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
    </resultMap>
</mapper>
