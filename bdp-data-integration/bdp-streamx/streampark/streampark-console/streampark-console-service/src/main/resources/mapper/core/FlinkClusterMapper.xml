<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.core.mapper.FlinkClusterMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.FlinkCluster">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="cluster_id" jdbcType="VARCHAR" property="clusterId"/>
        <result column="cluster_name" jdbcType="VARCHAR" property="clusterName"/>
        <result column="options" jdbcType="LONGVARCHAR" property="options"/>
        <result column="yarn_queue" jdbcType="VARCHAR" property="yarnQueue"/>
        <result column="execution_mode" jdbcType="TINYINT" property="executionMode"/>
        <result column="version_id" jdbcType="BIGINT" property="versionId"/>
        <result column="k8s_namespace" jdbcType="VARCHAR" property="k8sNamespace"/>
        <result column="service_account" jdbcType="VARCHAR" property="serviceAccount"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="flink_image" jdbcType="VARCHAR" property="flinkImage"/>
        <result column="dynamic_properties" jdbcType="LONGVARCHAR" property="dynamicProperties"/>
        <result column="k8s_rest_exposed_type" jdbcType="TINYINT" property="k8sRestExposedType"/>
        <result column="k8s_hadoop_integration" jdbcType="BOOLEAN" property="k8sHadoopIntegration"/>
        <result column="k8s_conf" jdbcType="VARCHAR" property="k8sConf"/>
        <result column="resolve_order" jdbcType="INTEGER" property="resolveOrder"/>
        <result column="exception" jdbcType="LONGVARCHAR" property="exception"/>
        <result column="cluster_state" jdbcType="TINYINT" property="clusterState"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
    </resultMap>

    <select id="existsByClusterId" resultType="java.lang.Boolean" parameterType="java.lang.String">
        select
         CASE
           WHEN  count(1) > 0 THEN true ELSE false
         END
        from t_flink_cluster
        <where>
            cluster_id=#{clusterId}
            <if test="id != null">
                and id &lt;&gt; #{id}
            </if>
        </where>
        limit 1
    </select>

    <select id="existsByClusterName" resultType="java.lang.Boolean" parameterType="java.util.Map">
        select
         CASE
           WHEN  count(1) > 0 THEN true ELSE false
         END
        from t_flink_cluster
        <where>
            cluster_name=#{clusterName}
            <if test="id != null">
                and id &lt;&gt; #{id}
            </if>
        </where>
        limit 1
    </select>

</mapper>
