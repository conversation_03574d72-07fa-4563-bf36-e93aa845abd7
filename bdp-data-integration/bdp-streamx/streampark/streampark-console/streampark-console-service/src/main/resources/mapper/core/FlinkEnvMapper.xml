<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.core.mapper.FlinkEnvMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.FlinkEnv">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="flink_name" jdbcType="VARCHAR" property="flinkName"/>
        <result column="flink_home" jdbcType="VARCHAR" property="flinkHome"/>
        <result column="flink_conf" jdbcType="VARCHAR" property="flinkConf"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="scala_version" jdbcType="VARCHAR" property="scalaVersion"/>
        <result column="is_default" jdbcType="BOOLEAN" property="isDefault"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
    </resultMap>

    <select id="getByAppId" resultType="org.apache.streampark.console.core.entity.FlinkEnv" parameterType="java.lang.Long">
        select v.*
        from t_flink_env v
        inner join (
            select version_id
            from t_flink_app
            where id = #{appId}
        ) as t
        on v.id = t.version_id
    </select>

    <update id="setDefault" parameterType="java.lang.Long">
        update t_flink_env
        set is_default = case id when #{id} then true else false end
    </update>
</mapper>
