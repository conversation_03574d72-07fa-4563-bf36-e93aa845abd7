<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.core.mapper.FlinkSqlMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.FlinkSql">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="sql" jdbcType="LONGVARCHAR" property="sql"/>
        <result column="candidate" jdbcType="INTEGER" property="candidate"/>
        <result column="dependency" jdbcType="VARCHAR" property="dependency"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
    </resultMap>

    <select id="getEffective" resultType="org.apache.streampark.console.core.entity.FlinkSql" parameterType="java.lang.Long">
        select s.*
        from t_flink_sql s
        inner join t_flink_effective e
        on s.id = e.target_id
        where e.target_type = 2
        and e.app_id = #{appId}
    </select>

    <select id="getLatestVersion" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select max(version) as maxVersion
        from t_flink_sql
        where app_id = #{appId}
    </select>

    <select id="getByTeamId" resultType="org.apache.streampark.console.core.entity.FlinkSql" parameterType="java.lang.Long">
        select s.*
        from t_flink_sql s
        inner join t_flink_app a
        on s.app_id = a.id
        where a.team_id = #{teamId}
    </select>

</mapper>
