<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.core.mapper.ProjectMapper">

    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.Project">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="repository" jdbcType="INTEGER" property="repository"/>
        <result column="branches" jdbcType="VARCHAR" property="branches"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="prvkey_path" jdbcType="VARCHAR" property="prvkeyPath"/>
        <result column="pom" jdbcType="VARCHAR" property="pom"/>
        <result column="build_args" jdbcType="VARCHAR" property="buildArgs"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="last_build" jdbcType="TIMESTAMP" property="lastBuild"/>
        <result column="build_state" jdbcType="INTEGER" property="buildState"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <select id="existsByTeamId" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        select
            CASE
                WHEN  count(1) > 0 THEN true ELSE false
            END
        from t_flink_project
            where team_id = #{teamId}
            limit 1
    </select>

    <select id="selectByTeamId" resultType="org.apache.streampark.console.core.entity.Project" parameterType="java.lang.Long">
        select *
        from t_flink_project
        where team_id = #{teamId}
    </select>

    <update id="updateBuildState" parameterType="java.util.Map">
        update t_flink_project
        set build_state = #{state}
        where id = #{id}
    </update>

    <update id="updateBuildTime" parameterType="java.lang.Long">
        update t_flink_project
        set last_build = now()
        where id = #{id}
    </update>

    <select id="page" resultType="org.apache.streampark.console.core.entity.Project" parameterType="org.apache.streampark.console.core.entity.Project">
        select *
        from t_flink_project t
        <where>
            t.team_id = #{project.teamId}
            <if test="project.name != null and project.name != ''">
                <if test="_databaseId == 'mysql'">
                    and t.name like concat('%', #{project.name},'%')
                </if>
                <if test="_databaseId == 'pgsql'">
                    and t.name like '%' || #{project.name} || '%'
                </if>
            </if>
            <if test="project.buildState != null">
                and t.build_state = #{project.buildState}
            </if>
        </where>
    </select>

    <select id="getBuildingCount" resultType="java.lang.Long">
        select count(1) from t_flink_project where build_state = 0
    </select>

</mapper>
