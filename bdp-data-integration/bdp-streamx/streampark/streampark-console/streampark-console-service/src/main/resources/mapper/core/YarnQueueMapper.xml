<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.core.mapper.YarnQueueMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.core.entity.YarnQueue">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="queue_label" jdbcType="VARCHAR" property="queueLabel"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
        <result column="modify_time" jdbcType="DATE" property="modifyTime"/>
    </resultMap>

    <select id="findQueues" resultType="org.apache.streampark.console.core.entity.YarnQueue">
        select * from t_yarn_queue
        <where>
            <if test="yarnQueue.teamId != null">
                team_id = #{yarnQueue.teamId}
            </if>
            <if test="yarnQueue.queueLabel != null and yarnQueue.queueLabel != ''">
                <if test="_databaseId == 'mysql'">
                    and queue_label like concat('%', #{yarnQueue.queueLabel},'%')
                </if>
                <if test="_databaseId == 'pgsql'">
                    and queue_label like '%' || #{yarnQueue.queueLabel} || '%'
                </if>
            </if>
            <if test="yarnQueue.createTimeFrom != null and yarnQueue.createTimeFrom !=''">
                and create_time &gt; #{yarnQueue.createTimeFrom}
            </if>
            <if test="yarnQueue.createTimeTo!= null and yarnQueue.createTimeTo !=''">
                and create_time &lt; #{yarnQueue.createTimeTo}
            </if>
        </where>
    </select>

    <select id="existsByQueueLabel" resultType="java.lang.Boolean" parameterType="org.apache.streampark.console.core.entity.YarnQueue">
        select
         CASE
          WHEN  count(1) > 0 THEN true ELSE false
         END
        from t_yarn_queue
        <where>
            team_id = #{yarnQueue.teamId}
            and
            queue_label = #{yarnQueue.queueLabel}
            <if test="yarnQueue.id != null">
                and id &lt;&gt; #{yarnQueue.id}
            </if>
        </where>
        limit 1
    </select>

</mapper>
