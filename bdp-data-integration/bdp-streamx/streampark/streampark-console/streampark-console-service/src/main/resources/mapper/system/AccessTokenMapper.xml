<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.system.mapper.AccessTokenMapper">

    <sql id="Base_Column_List">
        t1.id,
        t1.user_id,
        t2.username,
        t1.token,
        t1.create_time,
        t1.modify_time,
        t1.description,
        t1.status as status,
        t2.status as userStatus,
        case
            when t1.status = 1 and t2.status = 1
                then 1
            else 0
        end
        as finalStatus
    </sql>

    <select id="page" resultType="org.apache.streampark.console.system.entity.AccessToken"
            parameterType="org.apache.streampark.console.system.entity.AccessToken">
        select
        <include refid="Base_Column_List"/>
        from t_access_token t1 join t_user t2 on t1.user_id = t2.user_id
        <where>
            <if test="accessToken.userId != null and accessToken.userId != ''">
                and t1.user_id = #{accessToken.userId}
            </if>
        </where>
    </select>

    <select id="getByUserId" resultType="org.apache.streampark.console.system.entity.AccessToken">
        select
        <include refid="Base_Column_List"/>
        from t_user t2
        join t_access_token t1
        on t1.user_id = t2.user_id
        where t1.user_id = #{userId}
    </select>

    <select id="getByUserToken" resultType="org.apache.streampark.console.system.entity.AccessToken">
        select
        <include refid="Base_Column_List"/>
        from t_user t2
        join t_access_token t1 on t1.user_id = t2.user_id
        where t1.user_id = #{userId}
        and t1.token = #{accessToken,jdbcType=VARCHAR}
    </select>

    <select id="getById" resultType="org.apache.streampark.console.system.entity.AccessToken">
        select
        <include refid="Base_Column_List"/>
        from t_access_token t1
        join t_user t2 on t1.user_id = t2.user_id
        where t1.id= #{id}
    </select>
</mapper>
