<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.system.mapper.MemberMapper">

    <resultMap id="BaseResultMap" type="org.apache.streampark.console.system.entity.Member">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <delete id="deleteByUserId">
        delete
        from t_member
        where user_id = #{userId}
    </delete>

    <delete id="deleteByRoleId">
        delete
        from t_member
        where role_id = #{roleId}
    </delete>

    <select id="findUsers" resultType="org.apache.streampark.console.system.entity.Member" parameterType="org.apache.streampark.console.system.entity.Member">
        select tur.*,
        u.username,
        r.role_name
        from t_member tur
        join t_user u
        on u.user_id = tur.user_id
        join t_role r
        on r.role_id = tur.role_id
        <where>
            tur.team_id = #{member.teamId}
            <if test="member.userName != null and member.userName != ''">
                <if test="_databaseId == 'mysql'">
                    and u.username like concat('%', #{member.userName},'%')
                </if>
                <if test="_databaseId == 'pgsql'">
                    and u.username like '%' || #{member.userName} || '%'
                </if>
            </if>
            <if test="member.roleName != null and member.roleName != ''">
                and r.role_name = #{member.roleName}
            </if>
            <if test="member.createTimeFrom != null and member.createTimeFrom !=''">
                and tur.create_time &gt; #{member.createTimeFrom}
            </if>
            <if test="member.createTimeTo!= null and member.createTimeTo !=''">
                and tur.create_time &lt; #{member.createTimeTo}
            </if>
        </where>
    </select>

    <select id="findUsersNotInTeam" resultType="org.apache.streampark.console.system.entity.User">
        select u.user_id,
            u.username,
            u.nick_name,
            u.user_type,
            u.login_type,
            u.status,
            u.email,
            u.create_time,
            u.modify_time,
            u.sex,
            u.description
        from t_user u
        where u.user_id
        not in (
            select u.user_id
            from t_user u join t_member m
            on m.team_id = #{teamId}
            and m.user_id = u.user_id
        )
    </select>

</mapper>
