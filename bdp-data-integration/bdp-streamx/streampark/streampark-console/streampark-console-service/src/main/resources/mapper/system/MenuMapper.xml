<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.system.mapper.MenuMapper">
    <resultMap id="menu" type="org.apache.streampark.console.system.entity.Menu">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="menu_id" jdbcType="BIGINT" property="menuId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="component" jdbcType="VARCHAR" property="component"/>
        <result column="perms" jdbcType="VARCHAR" property="perms"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="type" jdbcType="CHAR" property="type"/>
        <result column="display" jdbcType="BOOLEAN" property="display"/>
        <result column="order_num" jdbcType="DOUBLE" property="orderNum"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <select id="findUserPermissions" resultType="string">
        select distinct m.perms
        from t_role r
        left join t_member ur on (r.role_id = ur.role_id)
        left join t_role_menu rm on (rm.role_id = r.role_id)
        left join t_menu m on (m.menu_id = rm.menu_id)
        where ur.user_id = #{userId}
        <if test="teamId != null">
            and ur.team_id = #{teamId}
        </if>
        and m.perms is not null
        and m.perms &lt;&gt; ''
    </select>

    <select id="findUserMenus" resultMap="menu">
        select m.*
        from t_menu m inner join t_role_menu rm
        on m.menu_id = rm.menu_id
        inner join t_member mr
        on rm.role_id = mr.role_id
        where m.type &lt;&gt; 1
        and mr.user_id = #{userId}
        and mr.team_id = #{teamId}
        order by m.order_num
    </select>

    <select id="findUserIdsByMenuId" parameterType="string" resultType="string">
        select user_id
        from t_member
        where role_id in
        (select rm.role_id from t_role_menu rm where rm.menu_id = #{menuId})
    </select>

</mapper>
