<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.system.mapper.TeamMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.system.entity.Team">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="team_name" jdbcType="VARCHAR" property="teamName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <select id="findTeam" resultType="org.apache.streampark.console.system.entity.Team" parameterType="org.apache.streampark.console.system.entity.Team">
        select * from t_team
        <where>
            <if test="team.teamName != null and team.teamName != ''">
                <if test="_databaseId == 'mysql'">
                    and team_name like concat('%', #{team.teamName},'%')
                </if>
                <if test="_databaseId == 'pgsql'">
                    and team_name like '%' || #{team.teamName} || '%'
                </if>
            </if>
            <if test="team.createTimeFrom != null and team.createTimeFrom !=''">
                and create_time &gt; #{team.createTimeFrom}
            </if>
            <if test="team.createTimeTo!= null and team.createTimeTo !=''">
                and create_time &lt; #{team.createTimeTo}
            </if>
        </where>
    </select>

    <select id="findUserTeams" resultType="org.apache.streampark.console.system.entity.Team" parameterType="org.apache.streampark.console.system.entity.Team">
        select t.* from t_team t
        join t_member m
        on t.id = m.team_id
        where m.user_id = #{userId}
    </select>
</mapper>
