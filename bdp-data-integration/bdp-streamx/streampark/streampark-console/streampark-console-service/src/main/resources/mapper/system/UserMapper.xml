<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.streampark.console.system.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="org.apache.streampark.console.system.entity.User">
        <id column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime"/>
        <result column="sex" jdbcType="CHAR" property="sex"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="last_team_id" jdbcType="BIGINT" property="lastTeamId"/>
        <result column="login_type" jdbcType="INTEGER" property="loginType"/>
    </resultMap>

    <sql id="userColumn">
        u.user_id,
        u.username,
        u.nick_name,
        u.user_type,
        u.login_type,
        u.status,
        u.email,
        u.create_time,
        u.modify_time,
        u.sex,
        u.description
    </sql>

    <select id="findUserDetail" resultType="org.apache.streampark.console.system.entity.User" parameterType="org.apache.streampark.console.system.entity.User">
        select <include refid="userColumn"></include>
        from t_user u
        <where>
            <if test="user.username != null and user.username != ''">
                <if test="_databaseId == 'mysql'">
                    and u.username like concat('%', #{user.username},'%')
                </if>
                <if test="_databaseId == 'pgsql'">
                    and u.username like '%' || #{user.username} || '%'
                </if>
            </if>
            <if test="user.createTimeFrom != null and user.createTimeFrom !=''">
                and u.create_time &gt; #{user.createTimeFrom}
            </if>
            <if test="user.createTimeTo!= null and user.createTimeTo !=''">
                and u.create_time &lt; #{user.createTimeTo}
            </if>
            <if test="user.sex != null and user.sex != ''">
                and u.sex = #{user.sex}
            </if>
            <if test="user.status != null and user.status != ''">
                and u.status = #{user.status}
            </if>
        </where>
    </select>

    <select id="getNoTokenUser" resultType="org.apache.streampark.console.system.entity.User" parameterType="org.apache.streampark.console.system.entity.User">
        select <include refid="userColumn"></include>
        from t_user u left join t_access_token t
        on u.user_id = t.user_id
        where t.user_id is null
    </select>

    <select id="findByAppOwner" resultType="org.apache.streampark.console.system.entity.User" parameterType="org.apache.streampark.console.system.entity.User">
        select <include refid="userColumn"></include>
        from t_user u inner join (
            select distinct(user_id) as user_id
            from t_flink_app
            where team_id = #{teamId}
        ) t
        on u.user_id = t.user_id
    </select>

    <update id="clearLastTeamByUserId" parameterType="java.lang.Long">
        update t_user
        set last_team_id = null
        where user_id = #{userId}
    </update>

    <update id="clearLastTeamByTeamId" parameterType="java.lang.Long">
        update t_user
        set last_team_id = null
        where last_team_id = #{teamId}
    </update>

</mapper>
