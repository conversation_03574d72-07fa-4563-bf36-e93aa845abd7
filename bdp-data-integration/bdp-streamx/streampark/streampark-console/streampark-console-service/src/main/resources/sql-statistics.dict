exception	227
datetime_interval_code	1
temporary	94
prepare	24
dec	10
references	17
year	88
leading	26
character_length	5
descriptor	1
without	817
path	96
trailing	9
trim	94
xml	215
rank	720
cube	71
hierarchy	25
coalesce	53
key_member	1
raw	37
version	923
extend	5
as	14065
contains	245
at	977
constraint_catalog	1
size	168
defaults	1
left	1232
domain	1
returns	77
parameter_specific_schema	1
top_level_count	1
object	416
relative	15
schema	354
subclass_origin	1
role	1
ordering	16
user_defined_type_code	1
result	968
characters	60
search	132
repeatable	1
by	6081
national	178
after	111
close	131
connect	27
a	31199
committed	1
c	19554
set	547
sets	148
message_octet_length	1
returned_sqlstate	1
g	1144
column	90
command_function_code	1
right	1296
union	635
k	1247
procedure	1
m	699
abs	77
cobol	1
under	2934
quarter	52
covar_pop	1
localtime	89
dispatch	1
language	724
hold	32
ref	15
pad	1
generated	33
prior	1
excluding	31
intersection	1
passthrough	1
exclude	10
others	210
ada	5
constraint_schema	1
add	544
current_path	1
static	509
count	4967
check	92
sql_tsi_week	4
fusion	1
collate	1
external	27
user_defined_type_catalog	1
specific_name	1
month	113
immediate	34
final	623
decimal	1198
locator	1
exec	22
cursor	1
some	916
server_name	1
percentile_disc	1
asensitive	1
key_type	1
session	52
for	3013
returned_length	1
session_user	1
connection_name	1
sql_tsi_year	3
character_set_name	1
routine_catalog	1
fortran	1
scope	3
timezone_minute	1
end	747
over	2089
intersect	175
varchar	1054
go	267
length	179
false	1810
label	1
current_role	1
with	2334
dynamic_function	1
parameter_ordinal_position	1
translation	5
grant	6
explain	128
decade	1
select	20227
called	43
nullable	80
breadth	1
catalog	293
release	13
deallocate	1
convert	26
localtimestamp	14
bit	16
timestampadd	57
scope_name	1
output	57
authorization	1
number	96
sqrt	31
else	624
normalize	1
action	9
join	2063
write	50
cascaded	1
sqlstate	1
order	1909
trigger_catalog	1
similar	117
returned_cardinality	1
uescape	3
large	223
in	4750
collation_schema	1
lower	50
stddev_samp	26
tinyint	305
is	3182
isolation	1
sensitive	19
ceil	76
sql_tsi_day	4
current_date	7
message_text	1
system	52
binary	40
condition_number	1
interval	863
begin	47
partial	103
server	2
deferred	1
user_defined_type_schema	1
message_length	1
parameter_specific_catalog	1
epoch	5
instantiable	1
restrict	2
dow	4
cycle	1
local	224
recursive	1
doy	4
current_default_transform_group	1
library	72
goto	1
stream	515
member	2
assertion	2
width_bucket	1
map	978
table	15795
current_user	1
catalog_name	1
within	26
max	1618
trigger	7
ordinality	1
century	4
names	89
equals	328
preceding	612
attributes	23
constraint	36
return	892
transaction	1
ln	26
instance	120
precision	150
nulls	727
modifies	1
integer	1290
second	865
row_count	1
function	382
limit	501
unnest	113
alter	44
all	810
always	115
new	5329
read	60
including	31
level	31
real	136
preserve	11
schema_name	1
condition	4609
null	7005
true	4336
position	28
collect	182
no	139
deref	1
delete	33
substring	303
routine_name	1
sql	3958
transform	23
hour	347
ties	11
savepoint	1
and	6742
current_timestamp	10
of	4292
first_value	50
regr_intercept	1
row	8686
floor	72
on	2381
or	4135
covar_samp	1
cross	23
specific	790
rows	627
character_set_catalog	1
pli	1
any	847
structure	1
minute	452
minvalue	28
command_function	1
inout	1
bytes	29
transactions_active	1
definer	1
regr_sxy	1
regr_sxx	1
upper	67
commit	11
insensitive	2
constraints	13
table_name	12
granted	1
character	245
java	892
char_length	18
merge	56
state	74
exp	39
defined	31
element	98
using	243
regr_syy	1
transactions_committed	1
old	256
module	22
outer	282
sql_tsi_minute	4
then	660
invoker	1
execute	548
each	14
uncommitted	1
input	105
microsecond	12
atomic	14
unique	86
except	782
asymmetric	3
referencing	2
deterministic	19
sum	3898
global	143
into	273
smallint	313
end-exec	1
default	159
current	374
min	1403
found	35
partition	1339
routine	1
varying	1
are	284
where	7341
attribute	53
free	101
escape	49
current_time	13
key	453
timestamp	1273
rollback	1
call	530
asc	962
varbinary	19
multiset	22
current_schema	1
diagnostics	1
bernoulli	1
absolute	7
regr_count	1
to	3727
describe	16
datetime_interval_precision	1
open	180
privileges	5
treat	19
declare	13
sql_tsi_second	5
source	8670
placing	18
lateral	229
view	123
whenever	1
continue	50
overlaps	9
dynamic	31
row_number	283
percent_rank	1
last	298
overlay	34
sql_tsi_hour	4
frac_second	2
last_value	58
constructor	5
degree	64
modules	11
extract	144
name	14071
nullif	24
mumps	1
full	402
desc	713
allow	53
next	167
drop	108
string	5024
import	2289
millennium	4
update	29
description	20
admin	1
revoke	1
sql_tsi_frac_second	2
symmetric	3
trigger_name	1
not	4515
avg	883
trigger_schema	1
statement	7
every	74
derived	5
characteristics	23
start	435
dense_rank	23
parameter_name	1
nchar	1
initially	6
time	1082
sql_tsi_quarter	4
window	965
regr_slope	1
disconnect	1
mod	183
maxvalue	24
usage	4
none	322
type	348
when	410
rollup	133
cast	1363
cume_dist	1
octet_length	1
options	87
connection	1
plan	959
case	1280
between	666
var_pop	36
having	72
natural	81
method	120
work	828
double	1456
upsert	7
scroll	1
grouping	324
allocate	3
following	319
reset	42
sqlwarning	1
matched	14
style	1
primary	151
regr_r2	1
columns	75
pascal	1
range	228
increment	2
sqlexception	1
stddev_pop	18
substitute	1
out	146
unknown	278
foreign	116
disallow	1
specifictype	1
identity	8
nclob	1
get	784
parameter_specific_name	1
power	55
nesting	1
system_user	1
percentile_cont	1
chain	1
restart	1
more	877
routine_schema	1
unnamed	3
sql_tsi_month	4
filter	868
current_transform_group_for_type	1
depth	1
current_catalog	1
fetch	417
char	58
self	105
exists	1122
first	773
date	797
indicator	2
corr	1
data	1885
returned_octet_length	1
before	547
transforms	3
scope_catalogs	1
var_samp	17
simple	85
wrapper	6
section	18
float	1007
translate	6
space	2
collation_catalog	1
zone	39
octets	1
only	704
create	749
from	11647
collation	16
day	305
dynamic_function_code	1
group	3718
user_defined_type_name	1
deferrable	1
offset	443
like	525
cascade	3
constraint_name	1
collation_name	1
inner	1193
serializable	4
sql_tsi_microsecond	2
character_set_schema	1
both	148
sequence	10
submultiset	1
blob	1
overriding	1
cursor_name	1
tablesample	1
option	57
scope_schema	1
week	24
regr_avgx	1
normalized	1
values	578
insert	271
distinct	1471
numeric	30
scale	137
regr_avgy	1
general	85
transactions_rolled_back	1
database	39
clob	1
security	1
public	1180
array	2222
parameter	15
unbounded	299
value	519
bigint	3003
ceiling	2
assignment	1
implementation	10
match	135
column_name	1
reads	1
timestampdiff	31
timezone_hour	1
int	4139
cardinality	17
boolean	1161
parameter_mode	1
corresponding	14
class_origin	1
user	54
