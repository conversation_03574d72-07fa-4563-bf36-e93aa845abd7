# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# public path
VITE_PUBLIC_PATH=/

# Cross-domain proxy, you can configure multiple
# Please note that no line breaks
VITE_PROXY=[["/basic-api","http://localhost:10000"]]

# Delete console
VITE_DROP_CONSOLE=false

# Basic interface address SPA
VITE_GLOB_API_URL=/basic-api

# File upload address， optional
VITE_GLOB_UPLOAD_URL=/upload

# Interface prefix
VITE_GLOB_API_URL_PREFIX=

VITE_APP_BASE_API=/basic-api
