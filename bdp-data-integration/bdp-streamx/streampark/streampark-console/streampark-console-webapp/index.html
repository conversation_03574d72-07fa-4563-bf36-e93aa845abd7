<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<!DOCTYPE html>
<html lang="en" id="htmlRoot">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="renderer" content="webkit" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0" />
  <title>
    <%= title %>
  </title>
  <link rel="icon" href="/favicon.ico" />
</head>

<body>
  <script>
    (() => {
      let htmlRoot = document.getElementById('htmlRoot');
      let theme = window.localStorage.getItem('__APP__DARK__MODE__');
      if (htmlRoot && theme) {
        htmlRoot.setAttribute('data-theme', theme);
        theme = htmlRoot = null;
      }
    })();
  </script>
  <div id="app">
    <style>
      html[data-theme='dark'] .app-loading {
        background-color: #2c344a;
      }

      html[data-theme='dark'] .app-loading .app-loading-title {
        color: rgb(255 255 255 / 85%);
      }

      .app-loading {
        display: flex;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background-color: #f4f7f9;
      }

      .app-loading .app-loading-wrap {
        position: absolute;
        top: 50%;
        left: 50%;
        display: flex;
        transform: translate3d(-50%, -50%, 0);
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }

      .app-loading .dots {
        display: flex;
        padding: 98px;
        justify-content: center;
        align-items: center;
      }

      .app-loading .app-loading-title {
        display: flex;
        margin-top: 30px;
        font-size: 30px;
        color: rgb(0 0 0 / 85%);
        justify-content: center;
        align-items: center;
      }

      .app-loading .app-loading-logo {
        display: block;
        width: 90px;
        margin: 0 auto;
        margin-bottom: 20px;
      }

      .dot {
        position: relative;
        display: inline-block;
        width: 48px;
        height: 48px;
        margin-top: 30px;
        font-size: 32px;
        transform: rotate(45deg);
        box-sizing: border-box;
        animation: antRotate 1.2s infinite linear;
      }

      .dot i {
        position: absolute;
        display: block;
        width: 20px;
        height: 20px;
        background-color: #0065cc;
        border-radius: 100%;
        opacity: 30%;
        transform: scale(0.75);
        animation: antSpinMove 1s infinite linear alternate;
        transform-origin: 50% 50%;
      }

      .dot i:nth-child(1) {
        top: 0;
        left: 0;
      }

      .dot i:nth-child(2) {
        top: 0;
        right: 0;
        animation-delay: 0.4s;
      }

      .dot i:nth-child(3) {
        right: 0;
        bottom: 0;
        animation-delay: 0.8s;
      }

      .dot i:nth-child(4) {
        bottom: 0;
        left: 0;
        animation-delay: 1.2s;
      }

      @keyframes antRotate {
        to {
          transform: rotate(405deg);
        }
      }

      @keyframes antRotate {
        to {
          transform: rotate(405deg);
        }
      }

      @keyframes antSpinMove {
        to {
          opacity: 100%;
        }
      }

      @keyframes antSpinMove {
        to {
          opacity: 100%;
        }
      }

    </style>
    <div class="app-loading">
      <div class="app-loading-wrap">
        <div class="app-loading-dots">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>
