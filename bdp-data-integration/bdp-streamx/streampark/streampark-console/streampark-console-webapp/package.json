{"name": "streampark-webapp", "version": "2.1.4", "author": {"name": "streampark", "url": "https://streampark.apache.org"}, "packageManager": "pnpm@7.3.0", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-streampark.git"}, "license": "Apache-2.0 license", "bugs": {"url": "https://github.com/apache/incubator-streampark/issues"}, "homepage": "https://streampark.apache.org", "scripts": {"bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite", "build": "cross-env NODE_ENV=production vite build && esno ./build/script/postBuild.ts", "build:test": "cross-env vite build --mode test && esno ./build/script/postBuild.ts", "build:no-cache": "npm run clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "test:unit": "jest", "test:gzip": "npx http-server dist --cors --gzip -c-1", "test:br": "npx http-server dist --cors --brotli -c-1", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "gen:icon": "esno ./build/generate/icon/index.ts"}, "dependencies": {"@ant-design/colors": "^7.0.2", "@ant-design/icons-vue": "^7.0.1", "@iconify/iconify": "^3.0.1", "@vue/runtime-core": "^3.4.19", "@vue/shared": "^3.4.19", "@vueuse/core": "^9.6.0", "@vueuse/shared": "^9.6.0", "@zxcvbn-ts/core": "^2.1.0", "ant-design-vue": "^3.2.20", "axios": "^1.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.6", "lodash-es": "^4.17.21", "monaco-editor": "^0.46.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "penpal": "^6.2.2", "pinia": "2.0.27", "qs": "^6.11.0", "resize-observer-polyfill": "^1.5.1", "sql-formatter": "^4.0.2", "sweetalert2": "^11.10.5", "sortablejs": "^1.15.0", "terser": "^5.16.1", "vue": "~3.3.13", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue-types": "^5.0.2"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@iconify/json": "^2.1.148", "@purge-icons/generated": "^0.9.0", "@types/fs-extra": "^9.0.13", "@types/lodash-es": "^4.17.6", "@types/node": "^18.11.11", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@types/showdown": "^2.0.0", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.45.1", "@typescript-eslint/parser": "^5.45.1", "@vitejs/plugin-legacy": "^2.2.0", "@vitejs/plugin-vue": "^3.1.2", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/compiler-sfc": "^3.2.47", "autoprefixer": "^10.4.13", "commitizen": "^4.2.5", "cross-env": "^7.0.3", "dotenv": "^16.0.3", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.8.0", "esno": "^0.16.3", "fs-extra": "^11.1.0", "less": "^4.1.3", "lint-staged": "13.1.0", "picocolors": "^1.0.0", "postcss": "^8.4.19", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.0", "rimraf": "^3.0.2", "rollup": "^3.20.1", "rollup-plugin-visualizer": "^5.9.0", "stylelint": "^14.16.0", "stylelint-config-prettier": "^9.0.4", "stylelint-config-recommended": "^9.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.2", "vite": "^3.2.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-theme": "^0.8.6", "vite-plugin-windicss": "^1.8.10", "vue-eslint-parser": "^9.1.0", "vue-tsc": "^1.2.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}, "engines": {"node": ">=16"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}}