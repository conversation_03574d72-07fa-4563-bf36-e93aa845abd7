/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  okText: 'OK',
  closeText: 'Close',
  detailText: 'Detail',
  submitText: 'Submit',

  failed: 'Failed',
  cancelText: 'Cancel',
  loadingText: 'Loading...',
  saveText: 'Save',
  delText: 'Delete',
  resetText: 'Reset',
  searchText: 'Search',
  queryText: 'Search',

  inputText: 'Please enter ',
  chooseText: 'Please choose ',

  redo: 'Refresh',
  back: 'Back',

  light: 'Light',
  dark: 'Dark',

  history: 'History',
  apply: 'Apply',

  next: 'Next',
  previous: 'Previous',

  view: 'View',
  detail: 'view detail',

  add: 'Add New',
  edit: 'Edit',

  yes: 'Yes',
  no: 'No',

  createTime: 'Create Time',
  modifyTime: 'Modify Time',
  description: 'Description',
};
