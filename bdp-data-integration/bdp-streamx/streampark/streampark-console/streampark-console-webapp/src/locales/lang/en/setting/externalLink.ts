/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  externalLinkSetting: 'External Link',
  form: {
    badgeLabel: 'Label',
    badgeLabelPlaceholder: 'Please enter label',
    badgeName: 'Name',
    badgeNamePlaceholder: 'Please enter name',
    badgeNameIsRequired: 'Name is required',
    badgeColor: 'Color',
    badgeColorIsRequired: 'Color is required',
    badgePreview: 'Preview',
    linkUrl: 'Link',
    linkUrlPlaceholder: 'Please enter link url',
    linkUrlIsRequired: 'Link is required',
  },
  operateMessage: {
    createLinkSuccessful: 'Successfully create external link!',
    createLinkFailed: 'Create external link failed!',
    updateLinkSuccessful: 'Successfully update external link!',
    updateLinkFailed: 'Update external link failed!',
    operationFailed: 'Operation failed',
  },
  confDeleteTitle: 'Are you sure delete this link record',
};
