/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  addToken: 'Add Token',
  modifyToken: 'Edit Token',
  copyToken: 'Copy Token',
  deleteToken: 'Delete Token',
  selectUserAlertMessage: 'Please select a user',
  table: {
    title: 'Token List',
    userName: 'User Name',
    token: 'Token',
    status: 'Status',
  },
  operation: {
    copySuccess: 'copy success',
    deleteTokenConfirm: 'are you sure delete this token ?',
    deleteSuccess: 'delete token successfully',
    deleteFailed: 'delete token failed',
    updateSuccess: 'update token successfully',
    createSuccess: 'create token successfully',
  },
};
