/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  userInfo: 'User Info',
  table: {
    title: 'User List',
    modify: 'modify user',
    reset: 'reset password',
    resetTip: 'reset password, are you sure',
    delete: 'delete user',
    deleteTip: 'delete user, are you sure',
    deleteSuccess: 'delete successful',
    resetSuccess: 'reset password successful, user [ {0} ] new password is streampark666',
  },
  form: {
    userName: 'User Name',
    required: 'username is required',
    min: 'username length cannot be less than 2 characters',
    max: 'exceeds maximum length limit of 20 characters',
    exist: 'Sorry the username already exists',
    nickName: 'Nick Name',
    userType: 'User Type',
    loginType: 'Login Type',
    status: 'Status',
    gender: 'Gender',
    lastLoginTime: 'Recent Login',
    password: 'Password',
    passwordRequire: 'password is required',
    passwordHelp: 'Password length cannot be less than 8 characters',
    email: 'please enter a valid email address',
    maxEmail: 'exceeds maximum length limit of 50 characters',
    create: 'Create User',
    edit: 'Edit User',
    view: 'View User',
  },
  roleInfo: 'Role Info',
  modifyTime: 'Not yet modified',
  male: 'male',
  female: 'female',
  secret: 'secret',
  locked: 'locked',
  effective: 'effective',
  resetSucceeded: 'Reset Succeeded',
  newPasswordTip: 'The new password is: ',
};
