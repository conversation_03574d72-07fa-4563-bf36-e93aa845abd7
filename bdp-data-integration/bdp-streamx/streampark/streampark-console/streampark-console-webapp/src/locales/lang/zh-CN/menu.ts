/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  menu: {
    system: '系统管理',
    userManagement: '用户管理',
    roleManagement: '角色管理',
    menuManagement: '菜单管理',
    tokenManagement: '令牌管理',
    teamManagement: '团队管理',
    memberManagement: '成员管理',
    project: '项目管理',
    application: '作业管理',
    variable: '变量管理',
    setting: '设置中心',
  },
  setting: {
    system: '系统设置',
    alarm: '告警设置',
    flinkHome: 'Flink 版本',
    flinkCluster: 'Flink 集群',
    externalLink: '扩展链接',
    yarnQueue: 'Yarn 队列',
  },
};
