/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  externalLinkSetting: '扩展链接',
  form: {
    badgeLabel: '徽章标签',
    badgeLabelPlaceholder: '请输入标签的名称',
    badgeName: '徽章名字',
    badgeNamePlaceholder: '请输入徽章名字',
    badgeNameIsRequired: '徽章名字是必填',
    badgeColor: '徽章颜色',
    badgeColorIsRequired: '徽章颜色是必选',
    badgePreview: '徽章预览',
    linkUrl: '链接',
    linkUrlPlaceholder: '请输入链接',
    linkUrlIsRequired: '链接是必填',
  },
  operateMessage: {
    createLinkSuccessful: '成功新建扩展链接!',
    createLinkFailed: '创建扩展链接失败!',
    updateLinkSuccessful: '成功更新扩展链接!',
    updateLinkFailed: '更新扩展链接失败!',
    operationFailed: '操作失败',
  },
  confDeleteTitle: '请确认删除该条记录',
};
