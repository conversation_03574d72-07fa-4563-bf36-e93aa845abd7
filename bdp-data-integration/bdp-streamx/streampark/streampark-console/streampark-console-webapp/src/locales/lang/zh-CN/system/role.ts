/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  deleteTip: '是否确定删除此角色',
  assignment: '菜单分配',
  form: {
    roleName: '角色名称',
    menuId: '权限',
    menuIdRequired: '请选择权限.',
    create: '创建角色',
    edit: '编辑角色',
    delete: '删除角色',
    view: '查看角色',
    noViewPermission: '必须要包含Application下的view权限',
    roleNameLen: '角色名称不应超过 255 个字符',
    exist: '角色名已存在',
    empty: '角色名不能为空',
  },
  roleInfo: '角色信息',
  tableTitle: '角色列表',
  modifyTime: '尚未修改',
};
