/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  addToken: '新增令牌',
  modifyToken: '编辑令牌',
  copyToken: '复制令牌',
  deleteToken: '删除令牌',
  selectUserAlertMessage: '请选择一个用户',
  table: {
    title: '令牌列表',
    userName: '用户名',
    token: '令牌',
    status: '状态',
  },
  operation: {
    copySuccess: '复制成功',
    deleteTokenConfirm: '您确定删除该令牌 ?',
    deleteSuccess: '删除成功',
    deleteFailed: '删除失败',
    updateSuccess: '更新成功',
    createSuccess: '创建成功',
  },
};
