/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  userInfo: '用户信息',
  table: {
    title: '用户列表',
    modify: '修改用户',
    reset: '重置密码',
    resetTip: '你确定要重置密码吗?',
    delete: '删除用户',
    deleteTip: '你确定要删除用户吗',
    deleteSuccess: '用户删除成功',
    resetSuccess: '重置密码成功，用户 [ {0} ] 新密码为 streampark666',
  },
  form: {
    userName: '用户名',
    required: '用户名为必填项',
    min: '用户名长度不能少于 2 个字符',
    max: '超过 20 个字符的最大长度限制',
    exist: '抱歉，用户名已存在',
    nickName: '昵称',
    userType: '用户类型',
    loginType: '登录类型',
    status: '状态',
    gender: '性别',
    lastLoginTime: '最近登录时间',
    password: '密码',
    passwordRequire: '密码为必填项',
    passwordHelp: '密码长度不能小于 8 个字符',
    email: '请输入有效的电子邮件地址',
    maxEmail: '超过 50 个字符的最大长度限制',
    create: '创建用户',
    edit: '编辑用户',
    view: '查看用户',
  },
  roleInfo: '角色信息',
  modifyTime: '尚未修改',
  male: '男',
  female: '女',
  secret: '未知',
  locked: '锁定',
  effective: '有效',
  resetSucceeded: '重置成功',
  newPasswordTip: '新的密码为：',
};
